## 文件说明
 
### 1. Copy of DpendenciesObjects.zip

密码：MongoDb

有诊主表（ENC）、收费明细表（CHARGE）、交易明细表（TXN），并通过存储过程实现账务数据的自动刷新、并发控制、日志记录，确保数据一致性和完整性。


#### 1.1 主要业务表

- PATIENT_ACCOUNT_DB_CHARGE

	记录每个就诊（ENCOUNTER）的收费明细，包括服务代码、金额、发票、支付、减免等信息。

	关键字段：ENCOUNTER_NUMBER, CHARGE_ID, SERVICE_CODE, CHARGE_AMT, PAYMENT_AMT_BY_PAYOR, INVOICE_NUMBER, LAST_PAYMENT_DATE_BY_PAYOR 等。


- PATIENT_ACCOUNT_DB_ENC
	
	记录就诊（ENCOUNTER）主信息，包括患者、医院、就诊类型、状态、最后一次支付代码、病房、账单截止日期等。
	
	关键字段：ENTITY_NUMBER, ENCOUNTER_ID, ENCOUNTER_NUMBER, CASE_TYPE, STATUS_CODE, LAST_PAY_CODE, LAST_WARD_CLASS, BILLED_UP_TO_DATE 等。

- PATIENT_ACCOUNT_DB_TXN

	记录交易明细，包括付款方、交易类型、金额、关联的收费、发票、减免、GL相关属性等。
	
	关键字段：PAYOR_ENTITY_NUMBER, TXN_ID, TRANSACTION_NUMBER, TRANS_TYPE, PAYMENT_METHOD, TRANSACTION_TOTAL_AMOUNT, CHARGE_ID, SERVICE_CODE, WAIVER_TYPE, GL_* 等。

- 其他基础表

	涉及大量以 BILLING_SCHEMA、PATIENT_ENQUIRY、REF_、OS_ 等为前缀的表，涵盖就诊、收费、发票、操作、交易、字典等各类信息。
	
	例如：AS_ENCOUNTER, BD_CHARGE, BD_TRANSACTION, BD_INVOICE, REF_PAYMENT_METHODS, REF_WAIVER_TYPE 等。

#### 1.2 存储过程

- GEN_BY_ENCOUNTER_NUMBER_V2

	- 作用：按就诊号刷新患者账务数据，支持并发控制，防止同一就诊号重复处理。
	- 主要流程：
		- 查询就诊ID，判断是否有并发处理，必要时等待。
		- 删除旧数据，插入新的就诊和收费明细。
		- 记录日志，更新控制表，支持调试模式。
	- 关键点：涉及大量业务表的联合查询和数据聚合，自动维护账务主表和明细表。

- GEN_BY_TRANSACTION_ID
	- 作用：按交易ID刷新账务交易明细，支持GL相关属性的补充。
	- 主要流程：
		- 删除旧交易明细，插入新数据。
		- 更新GL相关字段，处理减免、冲销等特殊交易。
	- 支持多种交易类型和来源，自动补全相关信息。


#### 函数分析

- GET_LAST_PAYMENT_DATE
	- 作用：根据收费ID获取最后一次付款时间。
	- 主要逻辑：通过多表关联，筛选付款类型交易，取最大时间。
	- 异常处理：如无付款则返回NULL。



 ### 2. Copy of Mongo DB POC - Spending Cap (1).pptx

- Mongo DB -> Oracle
- Oracle -> Mongo DB
- 吞吐、负载/压测报告，需提供基准测试的硬件配置和设置
- 根据提供的 Oracle DB 大小和所需的吞吐量，提供预算所需的硬件要求和成本信息
	- TPS:
		```
				Node1        Node2
		MAX  6712775       5320015
		TPS  1864.659722   1477.781944
		```
	- 提供设置所有非生产和生产环境的总成本估算（硬件、许可证、服务等）

- 附录
	```
	Oracle DB Version: 19.22
	OS Version: RHEL 8.4
	Number of RAC Nodes: 2
	CPU: 36 Cores
	RAM: 512GB
	DB Size: 22TB
	Archive Log size: 2GB
	Physical Standby: Maximum Performance Mode
	```