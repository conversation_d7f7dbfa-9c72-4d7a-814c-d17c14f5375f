# 1. Generate encounter & charges
Stored procedure name: PATIENT_ENQUIRY.GEN_BY_ENCOUNTER_V2

## Source table: to be provide by oracle dump file
- BILLING_SCHEMA.AS_ENCOUNTER
- BILLING_SCHEMA.AS_ENCOUNTER_EXTRA
- BILLING_SCHEMA.AS_ENCOUNTER_FIN_CLASS_HISTORY
- BILLING_SCHEMA.AS_ENCOUNTER_LOCATION
- BILLING_SCHEMA.AS_ENTITY_DETAIL
- BILLING_SCHEMA.BD_CHARGE
- BILLING_SCHEMA.BD_CHARGE_COMPONENT
- BILLING_SCHEMA.BD_CHARGE_EXTRA
- BILLING_SCHEMA.BD_INVOICE
- BILLING_SCHEMA.BD_OPERATION
- BILLING_SCHEMA.BD_OPERATION_ITEM
- BILLING_SCHEMA.BD_TRANSACTION
- BILLING_SCHEMA.BD_TRANSACTION_ITEM
- BILLING_SCHEMA.OS_ORGANISATION
- BILLING_SCHEMA.REF_ENCOUNTER_STATUS
- BILLING_SCHEMA.REF_ENCOUNTER_TYPE
- BILLING_SCHEMA.REF_FIN_CLASS
- BILLING_SCHEMA.REF_LOCATION
- BILLING_SCHEMA.REF_LOCATION_GROUP
- BILLING_SCHEMA.REF_REFERRAL_SOURCE
- BILLING_SCHEMA.REF_SPECIALTY
- BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE
- BILLING_SCHEMA.REF_TRANSACTION_SUBTYPE

## Destination Table
### Result Table
- PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_CHARGE
- PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_ENC

### Control and logging
- PATIENT_ENQUIRY.PADB_ENC_REGEN_CONTROL
- PATIENT_ENQUIRY.PATIENT_ACCOUNT_LOG

### Common function
- PATIENT_ENQUIRY.GET_LAST_PAYMENT_DATE


# 2. Generate transactions
Stored procedure name: PATIENT_ENQUIRY.GEN_BY_TRANSACTION_ID

## Source table
- BILLING_SCHEMA.AS_ENCOUNTER
- BILLING_SCHEMA.AS_ENTITY_DETAIL
- BILLING_SCHEMA.AS_WAIVER_SNAPSHOT
- BILLING_SCHEMA.BD_CHARGE
- BILLING_SCHEMA.BD_CHARGE_COMPONENT
- BILLING_SCHEMA.BD_CHARGE_EXTRA
- BILLING_SCHEMA.BD_INVOICE
- BILLING_SCHEMA.BD_TRANSACTION
- BILLING_SCHEMA.BD_TRANSACTION_ITEM
- BILLING_SCHEMA.BD_TRANS_CHARGE_ALLOC_CODE
- BILLING_SCHEMA.OS_ORGANISATION
- BILLING_SCHEMA.REF_CHARGE_EXTRA
- BILLING_SCHEMA.REF_PAYMENT_METHODS
- BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE
- BILLING_SCHEMA.REF_TRANSACTION_SUBTYPE
- BILLING_SCHEMA.REF_WAIVER_SOURCE
- BILLING_SCHEMA.REF_WAIVER_TYPE
## Destination Table
### Result Table
- PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_TXN
