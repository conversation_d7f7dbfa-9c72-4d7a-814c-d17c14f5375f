create or replace PROCEDURE GEN_BY_TRANSACTION_ID ( 
/*
*    BUILD INDEX ON 
*                   
*       PROCEDURE : GEN_BY_TRANSACTION_ID
*
*       DESCRIPTION : FOR THE PATIENT ACCOUNT ENQUIRY DATABASE, REFRESHED THE DATA BY TRANSACTION NUMBER
*
*       INPUT :
*       VARIABLE                TYPE                                         DESCRIPTION
*       --------                ----                                         -----------
*       IN_TXN_ID   VARCHAR2                                     TRANSACTION NUMBER
*
*       LOG :
*       DATE                    WHO                     ACTION
*       ----                    ---                     ------
*       ********                DAVID YU                INITIAL VERSION
*       ********                DAVUD YU                Payha db version, add db-link 
*       ********                DY                      add last_update_time
*       ********                DY                      add GL related attribute from BD_CHARGE_EXTRA
*       ********                DY                      add new column for waiver and charge allocation code
*
*
******************************************************************************/
   IN_TXN_ID IN VARCHAR2
)
IS
    N_ROWCOUNT NUMBER(10);
BEGIN
    DELETE PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_TXN WHERE TXN_ID = IN_TXN_ID;
    COMMIT;

    -- INSERT ALLOCATION AND CHILD TRANSACTION
    INSERT INTO PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_TXN(
        PAYOR_ENTITY_NUMBER, PAYOR_ENTITY_ID, PAYOR_PI_KEY, TXN_ID, TRANSACTION_NUMBER, TRANSACTION_HOSP, TRANSACTION_SOURCE, DATE_TIME_ENTERED, 
        DATE_TIME_ENACTED, TRANS_TYPE, TRANS_SUBTYPE, PAYMENT_METHOD, TRANSACTION_TOTAL_AMOUNT, REMAING_OR_ALLOCATED_AMOUNT, LAST_ALLOC_DATETIME, 
        ENCOUNTER_NUMBER, ENCOUNTER_HOSP, CHARGE_ID, SERVICE_CODE, SERVICE_START_DATE_TIME, SERVICE_DESC_PBRC, SERVICE_DESC_ENG, 
        SERVICE_DESC_CHI, INVOICE_NUMBER, INVOICE_ID, INVOICE_HOSP, TRANS_CHARGE_ALLOC_CODE, WAIVER_SNAPSHOT_ID
    )
    SELECT
        ENT.ENTITY_NUMBER AS PAYOR_ENTITY_NUMBER,
        ENT.ID AS PAYOR_ENTITY_ID,
        ENT.ENTITY_SECONDARY_ID AS PAYOR_PI_KEY,
        TXN.ID AS TXN_ID,
        TXN.TRANSACTION_NUMBER AS TRANSACTION_NUMBER,
        ORG.CODE AS TRANSACTION_HOSP,
        -- SUBSTR(TXN.TRANSACTION_NUMBER,1,5),
        CASE 
            WHEN TXN.TRANSACTION_NUMBER LIKE 'AE%' THEN 'AE' 
            WHEN TXN.TRANSACTION_NUMBER LIKE 'FCS%' THEN 'FCS'
            WHEN TXN.TRANSACTION_NUMBER LIKE 'OPAS%' THEN 'OPAS'
            ELSE 'PBRC'
        END AS TRANSACTION_SOURCE,
        TXN.DATE_TIME_ENTERED,
        TXN.DATE_TIME_ENACTED,
        ALLOC.TRANS_TYPE,
        CASE WHEN TXN.TRANSACTION_NUMBER LIKE 'WAI%' THEN 'Waiver' ELSE ST.NAME END AS TRANS_SUBTYPE,
        PM.NAME AS PAYMENT_METHOD,
        TXN.TOTAL_AMOUNT AS TRANSACTION_TOTAL_AMOUNT,
        SUM(TI.AMOUNT * -1) AS REMAING_OR_ALLOCATED_AMOUNT,
        MAX(ALLOC.DATE_TIME_ENTERED) AS LAST_ALLOC_DATETIME,
        ENC.ENCOUNTER_NUMBER,
        ENC_ORG.CODE AS ENCOUNTER_HOSP,
        CHG.ID AS CHARGE_ID,
        CHG.CODE AS SERVICE_CODE,
        CHG.SERVICE_START_DATE_TIME,
        CHG.DESCRIPTION1 AS SERVICE_DESC_PBRC,
        CHG.DESCRIPTION2 AS SERVICE_DESC_ENG, 
        CHG.DESCRIPTION3 AS SERVICE_DESC_CHI,
        INV.INVOICE_NUMBER,
        INV.ID AS INVOICE_ID,
        INV_ORG.CODE AS INVOICE_HOSP,
        CHG.ALLOCATION_CODE,
        TXN.REF_WAIVER_SNAPSHOT_ID
    FROM BILLING_SCHEMA.BD_TRANSACTION TXN
    INNER JOIN BILLING_SCHEMA.OS_ORGANISATION ORG ON TXN.TRANSACTION_ORG_ID = ORG.ID
    INNER JOIN BILLING_SCHEMA.AS_ENTITY_DETAIL ENT ON TXN.PAYOR_ENTITY_DETAIL_ID = ENT.ID
    INNER JOIN BILLING_SCHEMA.BD_TRANSACTION ALLOC ON TXN.ID = ALLOC.REF_TRANSACTION_ID
    INNER JOIN BILLING_SCHEMA.BD_TRANSACTION_ITEM TI ON ALLOC.ID = TI.TRANSACTION_ID
    INNER JOIN BILLING_SCHEMA.BD_CHARGE_COMPONENT CC ON TI.CHARGE_COMPONENT_ID = CC.ID
    INNER JOIN BILLING_SCHEMA.BD_CHARGE CHG ON CC.CHARGE_ID = CHG.ID
    INNER JOIN BILLING_SCHEMA.BD_INVOICE INV ON CC.INVOICE_ID = INV.ID
    INNER JOIN BILLING_SCHEMA.OS_ORGANISATION INV_ORG ON INV.ORGANISATIONAL_ID = INV_ORG.ID
    INNER JOIN BILLING_SCHEMA.AS_ENCOUNTER ENC ON CHG.ENCOUNTER_ID = ENC.ID
    INNER JOIN BILLING_SCHEMA.OS_ORGANISATION ENC_ORG ON ENC.ORGANISATIONAL_ID = ENC_ORG.ID
    LEFT OUTER JOIN BILLING_SCHEMA.REF_TRANSACTION_SUBTYPE ST ON TXN.TRANSACTION_SUBTYPE_ID = ST.ID
    LEFT OUTER JOIN BILLING_SCHEMA.REF_PAYMENT_METHODS PM ON TXN.PAYMENT_METHOD_ID = PM.ID
    WHERE TXN.ID = IN_TXN_ID
    GROUP BY ENT.ENTITY_NUMBER, ENT.ID, ENT.ENTITY_SECONDARY_ID, TXN.ID, TXN.TRANSACTION_NUMBER, ORG.CODE,
    CASE WHEN TXN.TRANSACTION_NUMBER LIKE 'AE%' THEN 'AE' WHEN TXN.TRANSACTION_NUMBER LIKE 'FCS%' THEN 'FCS' WHEN TXN.TRANSACTION_NUMBER LIKE 'OPAS%' THEN 'OPAS' ELSE 'PBRC' END,
    TXN.DATE_TIME_ENTERED, TXN.DATE_TIME_ENACTED, ALLOC.TRANS_TYPE, ST.NAME,
    PM.NAME, TXN.TOTAL_AMOUNT, ENC.ENCOUNTER_NUMBER, CHG.ID, CHG.CODE, CHG.SERVICE_START_DATE_TIME, CHG.DESCRIPTION1, 
    CHG.DESCRIPTION2, CHG.DESCRIPTION3, INV.INVOICE_NUMBER, INV.ID, TXN.REF_WAIVER_SNAPSHOT_ID, INV_ORG.CODE, ENC_ORG.CODE, CHG.ALLOCATION_CODE
    HAVING SUM(TI.AMOUNT * -1) <> 0
    ; 
    N_ROWCOUNT := SQL%ROWCOUNT;
    COMMIT;

    -- ******** ADD GL RELATED INFORMATION
    IF N_ROWCOUNT > 0 THEN
        BEGIN
            UPDATE PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_TXN TXN
            SET (
                GL_PAY_CODE, GL_CASE_TYPE, GL_EIS_SERVICE_TYPE, GL_EIS_SPECIALTY, GL_EIS_SUB_SPECIALTY, GL_OPAS_SHROFF_GROUP, 
                GL_OPAS_SPECIALTY, GL_MISC, GL_ENCOUNTER_MISC, GL_SERVICE_MISC, GL_WARD_CLASS, GL_WARD_CODE
            ) = (
                SELECT
                    MAX(CASE WHEN EXTRA_NAME = 'GL Pay Code' THEN EXTRA_VALUE ELSE NULL END) AS GL_PAY_CODE, 
                    MAX(CASE WHEN EXTRA_NAME = 'GL Case Type' THEN EXTRA_VALUE ELSE NULL END) AS GL_CASE_TYPE, 
                    MAX(CASE WHEN EXTRA_NAME = 'GL EIS Service Type' THEN EXTRA_VALUE ELSE NULL END) AS GL_EIS_SERVICE_TYPE,
                    MAX(CASE WHEN EXTRA_NAME = 'GL EIS Specialty' THEN EXTRA_VALUE ELSE NULL END) AS GL_EIS_SPECIALTY,
                    MAX(CASE WHEN EXTRA_NAME = 'GL EIS Sub-specialty' THEN EXTRA_VALUE ELSE NULL END) AS GL_EIS_SUB_SPECIALTY,
                    MAX(CASE WHEN EXTRA_NAME = 'GL OPAS Shroff Group' THEN EXTRA_VALUE ELSE NULL END) AS GL_OPAS_SHROFF_GROUP,
                    MAX(CASE WHEN EXTRA_NAME = 'GL OPAS Specialty' THEN EXTRA_VALUE ELSE NULL END) AS GL_OPAS_SPECIALTY,
                    MAX(CASE WHEN EXTRA_NAME = 'GL Misc' THEN EXTRA_VALUE ELSE NULL END) AS GL_MISC,
                    MAX(CASE WHEN EXTRA_NAME = 'GL Encounter Misc' THEN EXTRA_VALUE ELSE NULL END) AS GL_ENCOUNTER_MISC,
                    MAX(CASE WHEN EXTRA_NAME = 'GL Service Misc' THEN EXTRA_VALUE ELSE NULL END) AS GL_SERVICE_MISC,
                    MAX(CASE WHEN EXTRA_NAME = 'GL Ward Class' THEN EXTRA_VALUE ELSE NULL END) AS GL_WARD_CLASS,
                    MAX(CASE WHEN EXTRA_NAME = 'GL Ward Code' THEN EXTRA_VALUE ELSE NULL END) AS GL_WARD_CODE
                FROM (
                    SELECT
                        RCE.NAME AS EXTRA_NAME,
                        CE.VALUE AS EXTRA_VALUE
                    FROM BILLING_SCHEMA.REF_CHARGE_EXTRA RCE 
                    LEFT OUTER JOIN BILLING_SCHEMA.BD_CHARGE_EXTRA CE ON CE.EXTRA_NAME_ID = RCE.ID AND CE.ENTITY_ID = TXN.CHARGE_ID
                )
            ),
            LAST_UPDATE_TIME = SYSTIMESTAMP
            WHERE TXN.TXN_ID = IN_TXN_ID;
            COMMIT;
        EXCEPTION WHEN OTHERS THEN
            UPDATE PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_TXN TXN
            SET (
                GL_PAY_CODE, GL_CASE_TYPE, GL_EIS_SERVICE_TYPE, GL_EIS_SPECIALTY, GL_EIS_SUB_SPECIALTY, GL_OPAS_SHROFF_GROUP, 
                GL_OPAS_SPECIALTY, GL_MISC, GL_ENCOUNTER_MISC, GL_SERVICE_MISC, GL_WARD_CLASS, GL_WARD_CODE
            ) = (
                SELECT 'ERR','ERR','ERR','ERR','ERR','ERR','ERR','ERR','ERR','ERR','ERR','ERR' FROM DUAL
            ),
            LAST_UPDATE_TIME = SYSTIMESTAMP
            WHERE TXN.TXN_ID = IN_TXN_ID;
            COMMIT;
        END;
    END IF;


    -- INSERT DEBTIR MEMO - CANCELLATION
    INSERT INTO PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_TXN
    ( PAYOR_ENTITY_NUMBER, PAYOR_ENTITY_ID, PAYOR_PI_KEY, TXN_ID, TRANSACTION_NUMBER, TRANSACTION_HOSP, TRANSACTION_SOURCE, DATE_TIME_ENTERED, DATE_TIME_ENACTED,
        TRANS_TYPE, TRANS_SUBTYPE, PAYMENT_METHOD, TRANSACTION_TOTAL_AMOUNT, REMAING_OR_ALLOCATED_AMOUNT, LAST_ALLOC_DATETIME, LAST_UPDATE_TIME,
        WAIVER_SNAPSHOT_ID)
    SELECT
        ENT.ENTITY_NUMBER,
        ENT.ID,
        ENT.ENTITY_SECONDARY_ID,
        TXN.ID,
        TXN.TRANSACTION_NUMBER,
        ORG.CODE,
        CASE 
            WHEN TXN.TRANSACTION_NUMBER LIKE 'AE%' THEN 'AE' 
            WHEN TXN.TRANSACTION_NUMBER LIKE 'FCS%' THEN 'FCS'
            WHEN TXN.TRANSACTION_NUMBER LIKE 'OPAS%' THEN 'OPAS'
            ELSE 'PBRC'
        END,
        TXN.DATE_TIME_ENTERED,
        TXN.DATE_TIME_ENACTED,
        ALLOC.TRANS_TYPE,
        ST.NAME,
        PM.NAME,
        TXN.TOTAL_AMOUNT,
        SUM(ALLOC.TOTAL_AMOUNT * -1),
        MAX(ALLOC.DATE_TIME_ENTERED),
        SYSTIMESTAMP,
        TXN.REF_WAIVER_SNAPSHOT_ID AS WAIVER_SNAPSHOT_ID
    FROM BILLING_SCHEMA.BD_TRANSACTION TXN
    INNER JOIN BILLING_SCHEMA.OS_ORGANISATION ORG ON TXN.TRANSACTION_ORG_ID = ORG.ID
    INNER JOIN BILLING_SCHEMA.AS_ENTITY_DETAIL ENT ON TXN.PAYOR_ENTITY_DETAIL_ID = ENT.ID
    INNER JOIN BILLING_SCHEMA.BD_TRANSACTION ALLOC ON TXN.ID = ALLOC.REF_TRANSACTION_ID
    LEFT OUTER JOIN BILLING_SCHEMA.REF_TRANSACTION_SUBTYPE ST ON ALLOC.TRANSACTION_SUBTYPE_ID = ST.ID
    LEFT OUTER JOIN BILLING_SCHEMA.REF_PAYMENT_METHODS PM ON ALLOC.PAYMENT_METHOD_ID = PM.ID
    WHERE TXN.ID = IN_TXN_ID
    AND   ALLOC.TRANS_TYPE IN ( 'DM' , 'RF' ) -- to include refund transaction
    GROUP BY ENT.ENTITY_NUMBER, ENT.ID, ENT.ENTITY_SECONDARY_ID, TXN.ID, TXN.TRANSACTION_NUMBER, ORG.CODE,
    CASE WHEN TXN.TRANSACTION_NUMBER LIKE 'AE%' THEN 'AE' WHEN TXN.TRANSACTION_NUMBER LIKE 'FCS%' THEN 'FCS' WHEN TXN.TRANSACTION_NUMBER LIKE 'OPAS%' THEN 'OPAS' ELSE 'PBRC' END,
    TXN.DATE_TIME_ENTERED, TXN.DATE_TIME_ENACTED, ALLOC.TRANS_TYPE, ST.NAME,
    PM.NAME, TXN.TOTAL_AMOUNT, TXN.REF_WAIVER_SNAPSHOT_ID
    HAVING SUM(ALLOC.TOTAL_AMOUNT * -1) <> 0; 
    N_ROWCOUNT := SQL%ROWCOUNT + N_ROWCOUNT;
    COMMIT;

    -- INSERT HEADER
    INSERT INTO PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_TXN
    ( PAYOR_ENTITY_NUMBER, PAYOR_ENTITY_ID, PAYOR_PI_KEY, TXN_ID, TRANSACTION_NUMBER, TRANSACTION_HOSP, TRANSACTION_SOURCE, DATE_TIME_ENTERED, DATE_TIME_ENACTED,
        TRANS_TYPE, TRANS_SUBTYPE, PAYMENT_METHOD, TRANSACTION_TOTAL_AMOUNT, REMAING_OR_ALLOCATED_AMOUNT, LAST_ALLOC_DATETIME, LAST_UPDATE_TIME,
        WAIVER_SNAPSHOT_ID)
    SELECT
        ENT.ENTITY_NUMBER,
        ENT.ID,
        ENT.ENTITY_SECONDARY_ID,
        TXN.ID,
        TXN.TRANSACTION_NUMBER,
        ORG.CODE,
        CASE 
            WHEN TXN.TRANSACTION_NUMBER LIKE 'AE%' THEN 'AE' 
            WHEN TXN.TRANSACTION_NUMBER LIKE 'FCS%' THEN 'FCS'
            WHEN TXN.TRANSACTION_NUMBER LIKE 'OPAS%' THEN 'OPAS'
            ELSE 'PBRC'
        END,
        TXN.DATE_TIME_ENTERED,
        TXN.DATE_TIME_ENACTED,
        TXN.TRANS_TYPE,
        CASE WHEN TXN.TRANSACTION_NUMBER LIKE 'WAI%' THEN 'Waiver' ELSE ST.NAME END,
        PM.NAME,
        TXN.TOTAL_AMOUNT,
        CASE 
            WHEN N_ROWCOUNT = 0 THEN TXN.TOTAL_AMOUNT 
            ELSE TXN.TOTAL_AMOUNT - (SELECT SUM(REMAING_OR_ALLOCATED_AMOUNT) FROM PATIENT_ACCOUNT_DB_TXN WHERE TXN_ID = IN_TXN_ID)
        END,
        CASE 
            WHEN N_ROWCOUNT = 0 THEN NULL
            ELSE (SELECT MAX(LAST_ALLOC_DATETIME) FROM PATIENT_ACCOUNT_DB_TXN WHERE TXN_ID = IN_TXN_ID)
        END,
        SYSTIMESTAMP,
        TXN.REF_WAIVER_SNAPSHOT_ID AS WAIVER_SNAPSHOT_ID
    FROM BILLING_SCHEMA.BD_TRANSACTION TXN
    INNER JOIN BILLING_SCHEMA.OS_ORGANISATION ORG ON TXN.TRANSACTION_ORG_ID = ORG.ID
    INNER JOIN BILLING_SCHEMA.AS_ENTITY_DETAIL ENT ON TXN.PAYOR_ENTITY_DETAIL_ID = ENT.ID
    LEFT OUTER JOIN BILLING_SCHEMA.REF_TRANSACTION_SUBTYPE ST ON TXN.TRANSACTION_SUBTYPE_ID = ST.ID
    LEFT OUTER JOIN BILLING_SCHEMA.REF_PAYMENT_METHODS PM ON TXN.PAYMENT_METHOD_ID = PM.ID
    WHERE TXN.ID = IN_TXN_ID;
    COMMIT;


    -- update charge allocation code for non-allocation txn
    UPDATE PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_TXN PADB_TXN
    SET TRANS_CHARGE_ALLOC_CODE = (
        -- ONLY UPDATE FOR MASTER TXN & PAYMENT ONLY
        CASE WHEN PADB_TXN.TRANS_TYPE = 'RC' THEN (
            SELECT
                LISTAGG(CHARGE_ALLOCATION_CODE, ' | ') WITHIN GROUP (ORDER BY TRANSACTION_DATE_TIME )
            FROM BILLING_SCHEMA.BD_TRANS_CHARGE_ALLOC_CODE
            WHERE REF_PAYOR_ID = PADB_TXN.PAYOR_ENTITY_ID
            AND   TRANSACTION_DATE_TIME = PADB_TXN.DATE_TIME_ENACTED
        )
        ELSE PADB_TXN.TRANS_CHARGE_ALLOC_CODE END   -- OTHERWISE REMAIN UNCHANGE
    ),( WAIVER_REF_NUMBER, WAIVER_TYPE, WAIVER_SOURCE ) = (
        SELECT
            WS.REFERENCE_ID,
            RWS.NAME AS WAIVER_SOURCE,
            RWT.NAME AS WAIVER_TYPE
        FROM BILLING_SCHEMA.AS_WAIVER_SNAPSHOT WS
        INNER JOIN BILLING_SCHEMA.REF_WAIVER_SOURCE RWS ON WS.SOURCE_ID = RWS.ID
        INNER JOIN BILLING_SCHEMA.REF_WAIVER_TYPE RWT ON WS.TYPE_ID = RWT.ID
        WHERE WS.ID = PADB_TXN.WAIVER_SNAPSHOT_ID
    ),TRANS_REASON_CODE = (
        SELECT NAME 
        FROM BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE
        WHERE ID = (
            SELECT TRANSACTION_REASON_ID FROM BILLING_SCHEMA.BD_TRANSACTION TXN
            WHERE ID = IN_TXN_ID
        )
    )
    WHERE PADB_TXN.TXN_ID = IN_TXN_ID
    ;
    COMMIT;

/**
    -- PATCH WAIVER INFORMATOIN FOR ALLOCATION
    UPDATE PATIENT_ENQUIRY.PATIENT_ACCOUNT_DB_TXN TXN
    SET ( WAIVER_REF_NUMBER, WAIVER_TYPE, WAIVER_SOURCE ) = (
        SELECT
            WS.REFERENCE_ID,
            RWS.NAME AS WAIVER_SOURCE,
            RWT.NAME AS WAIVER_TYPE
        FROM BILLING_SCHEMA.AS_WAIVER_SNAPSHOT WS
        INNER JOIN BILLING_SCHEMA.REF_WAIVER_SOURCE RWS ON WS.SOURCE_ID = RWS.ID
        INNER JOIN BILLING_SCHEMA.REF_WAIVER_TYPE RWT ON WS.TYPE_ID = RWT.ID
        WHERE WS.ID = TXN.WAIVER_SNAPSHOT_ID
    )
    WHERE TXN.TXN_ID = IN_TXN_ID
    AND   TXN.WAIVER_SNAPSHOT_ID IS NOT NULL;
    COMMIT;

    -- update transaction reason code
    UPDATE PATIENT_ACCOUNT_DB_TXN PADB_TXN
    SET TRANS_REASON_CODE = (
        SELECT NAME 
        FROM BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE
        WHERE ID = (
            SELECT TRANSACTION_REASON_ID FROM BILLING_SCHEMA.BD_TRANSACTION TXN
            WHERE ID = IN_TXN_ID
        )
    )
    WHERE PADB_TXN.TXN_ID = IN_TXN_ID;
    COMMIT;
**/

END;