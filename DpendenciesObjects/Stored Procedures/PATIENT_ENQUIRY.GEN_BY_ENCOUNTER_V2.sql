create or replace PROCEDURE GEN_BY_ENCOUNTER_NUMBER_V2 ( 
/*
*    BUILD INDEX ON 
*                   
*       PROCEDURE : GEN_BY_ENCOUNTER_NUMBER
*
*       DESCRIPTION : FOR THE PATIENT ACCOUNT ENQUIRY DATABASE, REFRESHED THE DATA BY ENCOUNTER NUMBER
*
*       INPUT :
*       VARIABLE                TYPE                                         DESCRIPTION
*       --------                ----                                         -----------
*       IN_ENCOUNTER_NUMBER     VARCHAR2                                     ENCOUNTER NUMBER
*       IN_GEN_TYPE             NUMBER                                       0: <PERSON><PERSON>OUNTER AND CHARGE, 1 ENCOUNTER ONLY, 2 CHARGE ONLY
*       IN_GEN_MODE             NUMBER                                       0: GEN ONLY / 1: DELETE & GEN
*
*       LOG :
*       DATE                    WHO                     ACTION
*       ----                    ---                     ------
*       ********                DAVID YU                INITIAL VERSION
*       ********                DAVID YU                ADD LOGGING
*       ********                DAVID YU                Add new column: last_payment_date_by_payor at patient_account_db_charge
*                                                       Bug fix on BILLED_TOTAL calculation
*       ********                DAVID YU                DB-LINK TO 7P1  
*       ********                DAVID YU                Add LAST_ALLOCATION_ID and LAST_OPERATION_ID
*       ********                DAVID YU                Add queue control mechanism, if same encounter are processing on other thread, hold and skip current request
*       ********                DAVID YU                Copy from PayHA db, remove db-link and run in PBRC db
*
    REGENERATE BY PAYOR ID


    DECLARE
        C_PAYOR_ID VARCHAR2(100 CHAR);
    BEGIN
        C_PAYOR_ID := 'A1234563';

        FOR RS IN (
            SELECT
                DISTINCT ENCOUNTER_NUMBER
            FROM AS_ENTITY_DETAIL ENT
            INNER JOIN AS_ENCOUNTER ENC ON ENC.RECIPIENT_ENTITY_DETAIL_ID = ENT.ID
            WHERE ENT.ENTITY_NUMBER = 'A1234563'
            ORDER BY ENCOUNTER_NUMBER
        )LOOP
            GEN_BY_ENCOUNTER_NUMBER(0, RS.ENCOUNTER_NUMBER, '2', '1');
        END LOOP;    
    END;


******************************************************************************/
   IN_QUEUE_ID         IN NUMBER,
   IN_ENCOUNTER_NUMBER IN VARCHAR2,
   IN_GEN_TYPE         IN NUMBER,
   IN_GEN_MODE         IN NUMBER
)
IS
    N_ENCOUNTER_ID          NUMBER(30);
    C_LAST_PAYCODE          VARCHAR2(20 CHAR);
    C_LAST_EIS_SPEC         VARCHAR2(20 CHAR);
    C_LAST_WARD_CLASS       VARCHAR2(20 CHAR);
    C_LAST_WARD_CODE        VARCHAR2(20 CHAR);
    T_BILLED_UP_TO          TIMESTAMP(6);

    C_IS_DEBUG              BOOLEAN;
    N_ROWCOUNT              NUMBER(30);

    N_LAST_ALLOCATION_ID    NUMBER(30);
    N_LAST_OPERATION_ID     NUMBER(30);

    SQLERRM_MSG VARCHAR2(2000 CHAR);
    SQLCODE_CDE NUMBER;

    T_START_TIME TIMESTAMP(6);
    N_CHARGE_CNT    NUMBER(20);

    -- DY ******** >>
    N_PARALLEL_PROCESSING_CNT   NUMBER(1); 
    C_IS_SKIP                   VARCHAR2(1 CHAR);
    -- DY ******** <<
BEGIN
    C_IS_DEBUG := FALSE;

    N_ENCOUNTER_ID := NULL;
    T_START_TIME := SYSTIMESTAMP;
    N_CHARGE_CNT := 0;


    IF C_IS_DEBUG = TRUE THEN
        INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '| INPUT IN_ENCOUNTER_NUMBER[' || IN_ENCOUNTER_NUMBER || '], IN_GEN_TYPE[' || IN_GEN_TYPE || '], IN_GEN_MODE[' || IN_GEN_MODE || ']' FROM DUAL;
        COMMIT;
    END IF;

    BEGIN
        SELECT ID INTO N_ENCOUNTER_ID FROM BILLING_SCHEMA.AS_ENCOUNTER WHERE ENCOUNTER_NUMBER = IN_ENCOUNTER_NUMBER;
        COMMIT; /** COMMIT AFTER DB-LINK SELECT **/ 
    /**
    EXCEPTION WHEN OTHERS THEN
        SQLERRM_MSG := SQLERRM;
        SQLCODE_CDE := SQLCODE;
        N_ENCOUNTER_ID := NULL;

        INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '  >> ERROR GETTING ENCOUNTER_ID [' || SQLCODE_CDE || ', ' || SQLERRM_MSG || ' ]' FROM DUAL;
        COMMIT;
    **/
    END;

    IF C_IS_DEBUG = TRUE THEN
        INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '|   >> N_ENCOUNTER_ID[' || NVL( N_ENCOUNTER_ID , -1 ) || ']' FROM DUAL;
        COMMIT;
    END IF;

    IF N_ENCOUNTER_ID IS NOT NULL THEN
        -- DY ******** : NEW FLOW CONTROL TO PREVENT CONCURRENT RE-GENERATION >>
        BEGIN
            C_IS_SKIP := 'N';
            N_PARALLEL_PROCESSING_CNT := 1;
            WHILE N_PARALLEL_PROCESSING_CNT > 0
            LOOP
                SELECT COUNT(1)
                INTO N_PARALLEL_PROCESSING_CNT
                FROM PATIENT_ENQUIRY.PADB_ENC_REGEN_CONTROL
                WHERE ENCOUNTER_NUMBER = IN_ENCOUNTER_NUMBER
                AND   COMPLETE_TIME IS NULL
                ;

                IF N_PARALLEL_PROCESSING_CNT > 0 THEN
                    C_IS_SKIP := 'Y';
                    DBMS_SESSION.SLEEP(2); -- wait 2 SECOND
                END IF;
            END LOOP;
        EXCEPTION WHEN OTHERS THEN
            C_IS_SKIP := 'Y';
        END;    

        -- INSERT NEW CONTROL
        INSERT INTO PATIENT_ENQUIRY.PADB_ENC_REGEN_CONTROL
        SELECT IN_ENCOUNTER_NUMBER, T_START_TIME, NULL, C_IS_SKIP FROM DUAL;
        COMMIT;

        IF C_IS_SKIP = 'N' THEN
            -- DY ******** : NEW FLOW CONTROL TO PREVENT CONCURRENT RE-GENERATION <<    




            IF IN_GEN_TYPE = 0 OR IN_GEN_TYPE = 1 THEN
                -- DELETE EXISTING >>
                IF IN_GEN_MODE = 1 THEN
                    DELETE PATIENT_ACCOUNT_DB_ENC WHERE ENCOUNTER_NUMBER = IN_ENCOUNTER_NUMBER;
                    COMMIT;
                END IF;

                -- GET LAST PAYCODE >>
                BEGIN
                    SELECT NAME INTO C_LAST_PAYCODE FROM (                   
                        SELECT FC.NAME, FCH.*
                        FROM BILLING_SCHEMA.AS_ENCOUNTER_FIN_CLASS_HISTORY FCH
                        INNER JOIN BILLING_SCHEMA.REF_FIN_CLASS FC ON FCH.FIN_CLASS_ID = FC.ID
                        WHERE ENCOUNTER_ID = N_ENCOUNTER_ID
                        ORDER BY START_DATE_TIME DESC
                    )WHERE ROWNUM = 1;

                    COMMIT; /** COMMIT AFTER DB-LINK SELECT **/ 
                EXCEPTION WHEN OTHERS THEN
                    C_LAST_PAYCODE := NULL;
                END;

                IF C_IS_DEBUG = TRUE THEN
                    INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '|   >> C_LAST_PAYCODE[' || C_LAST_PAYCODE || ']' FROM DUAL;
                    COMMIT;
                END IF;

                -- GET LAST WARD >>
                BEGIN
                    SELECT WARD_CODE, WARD_CLASS
                    INTO C_LAST_WARD_CODE, C_LAST_WARD_CLASS 
                    FROM (                   
                        SELECT 
                            RL.LOCATION_NUMBER AS WARD_CODE, LG.NAME AS WARD_CLASS
                        FROM BILLING_SCHEMA.AS_ENCOUNTER_LOCATION LOC 
                        INNER JOIN BILLING_SCHEMA.REF_LOCATION RL ON LOC.LOCATION_ID = RL.ID
                        LEFT OUTER JOIN BILLING_SCHEMA.REF_LOCATION_GROUP LG ON LOC.LOCATION_GROUP_ID = LG.ID
                        WHERE LOC.ENCOUNTER_ID = N_ENCOUNTER_ID
                        ORDER BY LOC.START_DATE_TIME DESC
                    )WHERE ROWNUM = 1;    

                    COMMIT; /** COMMIT AFTER DB-LINK SELECT **/ 
                EXCEPTION WHEN OTHERS THEN
                    C_LAST_WARD_CODE := NULL;
                    C_LAST_WARD_CLASS := NULL;
                END;

                IF C_IS_DEBUG = TRUE THEN
                    INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '|   >> C_LAST_WARD_CODE[' || C_LAST_WARD_CODE || ']' FROM DUAL;
                    COMMIT;
                    INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '|   >> C_LAST_WARD_CLASS[' || C_LAST_WARD_CLASS || ']' FROM DUAL;
                    COMMIT;
                END IF;

                -- GET LAST INVOICE >>
                BEGIN
                    SELECT
                        MAX(INV.ISSUE_DATE)
                    INTO T_BILLED_UP_TO
                    FROM BILLING_SCHEMA.BD_CHARGE CHG 
                    INNER JOIN BILLING_SCHEMA.BD_CHARGE_COMPONENT CC ON CHG.ID = CC.CHARGE_ID
                    INNER JOIN BILLING_SCHEMA.BD_INVOICE INV ON CC.INVOICE_ID = INV.ID
                    WHERE CHG.ENCOUNTER_ID = N_ENCOUNTER_ID;

                    COMMIT; /** COMMIT AFTER DB-LINK SELECT **/ 
                EXCEPTION WHEN OTHERS THEN
                    T_BILLED_UP_TO := NULL;
                END;

                IF C_IS_DEBUG = TRUE THEN
                    INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '|   >> T_BILLED_UP_TO[' || T_BILLED_UP_TO || ']' FROM DUAL;
                    COMMIT;
                END IF;

                -- GET EIS FOR IP/AE >>
                BEGIN            
                    SELECT NAME 
                    INTO C_LAST_EIS_SPEC
                    FROM (
                        SELECT
                            SPEC.NAME
                        FROM BILLING_SCHEMA.AS_ENCOUNTER_LOCATION EL
                        LEFT OUTER JOIN BILLING_SCHEMA.REF_SPECIALTY SPEC ON EL.SPECIALTY1_ID = SPEC.ID
                        WHERE EL.ENCOUNTER_ID = N_ENCOUNTER_ID
                        ORDER BY START_DATE_TIME DESC                    
                    )WHERE ROWNUM = 1;

                    COMMIT; /** COMMIT AFTER DB-LINK SELECT **/ 
                EXCEPTION WHEN OTHERS THEN
                    C_LAST_EIS_SPEC := NULL;
                END;
                -- GET EIS FOR IP/AE <<

                IF C_IS_DEBUG = TRUE THEN
                    INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '|   >> C_LAST_EIS_SPEC[' || C_LAST_EIS_SPEC || ']' FROM DUAL;
                    COMMIT;
                END IF;

                -- DY ******** >> 
                BEGIN
                    SELECT
                        NVL(MAX(ALLOC.ID),0 )
                    INTO N_LAST_ALLOCATION_ID
                    FROM BILLING_SCHEMA.BD_CHARGE CHG
                    INNER JOIN BILLING_SCHEMA.BD_CHARGE_COMPONENT CC ON CHG.ID = CC.CHARGE_ID
                    INNER JOIN BILLING_SCHEMA.BD_TRANSACTION_ITEM TI ON TI.CHARGE_COMPONENT_ID = CC.ID
                    INNER JOIN BILLING_SCHEMA.BD_TRANSACTION ALLOC ON TI.TRANSACTION_ID = ALLOC.ID
                    WHERE CHG.ENCOUNTER_ID = N_ENCOUNTER_ID;

                    COMMIT; /** COMMIT AFTER DB-LINK SELECT **/ 
                EXCEPTION WHEN OTHERS THEN
                    N_LAST_ALLOCATION_ID := NULL;
                END;

                BEGIN
                    SELECT
                        NVL(MAX(O.ID),0)
                    INTO N_LAST_OPERATION_ID
                    FROM BILLING_SCHEMA.BD_CHARGE CHG
                    INNER JOIN BILLING_SCHEMA.BD_CHARGE_COMPONENT CC ON CHG.ID = CC.CHARGE_ID
                    INNER JOIN BILLING_SCHEMA.BD_OPERATION_ITEM OI ON OI.CHARGE_COMPONENT_ID = CC.ID
                    INNER JOIN BILLING_SCHEMA.BD_OPERATION O ON OI.OPERATION_ID = O.ID
                    WHERE CHG.ENCOUNTER_ID = N_ENCOUNTER_ID;

                    COMMIT; /** COMMIT AFTER DB-LINK SELECT **/ 
                EXCEPTION WHEN OTHERS THEN
                    N_LAST_OPERATION_ID := NULL;            
                END;
                -- DY ******** <<

                IF C_IS_DEBUG = TRUE THEN
                    INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '|   >> N_ENCOUNTER_ID[' || N_ENCOUNTER_ID || '], INSERT INTO PATIENT_ACCOUNT_DB_ENC' FROM DUAL;
                    COMMIT;
                END IF;

                INSERT INTO PATIENT_ACCOUNT_DB_ENC
                SELECT
                    ENT.ENTITY_NUMBER,
                    ORG.CODE AS HOSP,
                    ENC.ID,
                    ENC.ENCOUNTER_NUMBER,
                    ENC.START_DATE_TIME,
                    ENC.END_DATE_TIME,
                    RET.NAME AS CASE_TYPE,
                    RES.NAME AS STATUS_CODE,
                    C_LAST_PAYCODE,
                    CASE
                        WHEN ENC.ENCOUNTER_TYPE_ID IN (1,3) THEN C_LAST_EIS_SPEC
                        ELSE EIS.VALUE
                    END AS EIS_SPEC_UDF,
                    C_LAST_WARD_CLASS,
                    C_LAST_WARD_CODE,
                    T_BILLED_UP_TO,
                    RRS.NAME AS REFERRAL_SOURCE,
                    SYSTIMESTAMP
                    , ENC.RECIPIENT_ENTITY_DETAIL_ID
                    , ENC.ENCOUNTER_TYPE_ID
                    , N_LAST_ALLOCATION_ID
                    , N_LAST_OPERATION_ID
                FROM BILLING_SCHEMA.AS_ENCOUNTER ENC
                INNER JOIN BILLING_SCHEMA.AS_ENTITY_DETAIL ENT ON ENC.RECIPIENT_ENTITY_DETAIL_ID = ENT.ID
                INNER JOIN BILLING_SCHEMA.OS_ORGANISATION ORG ON ENC.ORGANISATIONAL_ID = ORG.ID
                INNER JOIN BILLING_SCHEMA.REF_ENCOUNTER_TYPE RET ON ENC.ENCOUNTER_TYPE_ID = RET.ID
                LEFT OUTER JOIN BILLING_SCHEMA.REF_ENCOUNTER_STATUS RES ON ENC.ENCOUNTER_STATUS_ID = RES.ID
                LEFT OUTER JOIN BILLING_SCHEMA.REF_REFERRAL_SOURCE RRS ON ENC.REFERRAL_SOURCE_ID = RRS.ID
                LEFT OUTER JOIN BILLING_SCHEMA.AS_ENCOUNTER_EXTRA EIS ON EIS.ENTITY_ID = ENC.ID AND EIS.EXTRA_NAME_ID = 55 -- EIS_SPEC
                WHERE ENC.ENCOUNTER_NUMBER = IN_ENCOUNTER_NUMBER;
                N_ROWCOUNT := SQL%ROWCOUNT;
                COMMIT;

                IF C_IS_DEBUG = TRUE THEN
                    INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '|   >> CONVERTED ENCOUNTER [' || N_ROWCOUNT || ']' FROM DUAL;
                    COMMIT;
                END IF;
            END IF;

            IF IN_GEN_TYPE = 0 OR IN_GEN_TYPE = 2 THEN
                IF IN_GEN_MODE = 1 THEN
                    DELETE PATIENT_ACCOUNT_DB_CHARGE WHERE ENCOUNTER_NUMBER = IN_ENCOUNTER_NUMBER;
                    COMMIT;
                END IF;

                IF C_IS_DEBUG = TRUE THEN
                    INSERT INTO PATIENT_ACCOUNT_LOG SELECT SYSTIMESTAMP, '|' || IN_QUEUE_ID || '|   >> N_ENCOUNTER_ID[' || N_ENCOUNTER_ID || '], INSERT INTO PATIENT_ACCOUNT_DB_CHARGE' FROM DUAL;
                    COMMIT;
                END IF;

                INSERT INTO PATIENT_ACCOUNT_DB_CHARGE
                SELECT
                    CHG.ENCOUNTER_NUMBER,
                    CHG.CHARGE_ID,
                    CHG.CHARGE_CODE AS SERVICE_CODE,    
                    CHG.SERVICE_START_DATE_TIME,
                    CHG.ENG_DESC,
                    CHG.CHT_DESC,
                    CHG.EIS_SPEC,
                    CHG.CHARGE_AMOUNT,
                    CHG.ADJUSTED_AMOUNT,
                    CASE WHEN CHG.INVOICE_ID IS NOT NULL THEN CHG.CHARGE_AMOUNT + CHG.ADJUSTED_AMOUNT ELSE 0 END AS BILLED_AMT,
                    NVL(ALLOC.WO_AMOUNT,0) AS WO_AMOUNT,
                    NVL(ALLOC.WAIVED_AMOUNT, 0 ) AS WAIVED_AMOUNT, 
                    NVL(ALLOC.WO_OFFSET_AMOUNT,0) AS WO_OFFSET_AMOUNT,    
                    NVL(ALLOC.PAID_BY_PAYOR, 0 ) AS PAID_BY_PAYOR, 
                    NVL(ALLOC.PAID_BY_OTHERS, 0 ) AS PAID_BY_OTHERS, 
                    CHG.INVOICE_NUMBER,
                    CHG.INVOICE_ID,  
                    CHG.INVOICE_HOSP,
                    CHG.PAYOR_ENTITY_NUMBER,
                    CHG.LAST_ADJUSTMENT_DATE,
                    ALLOC.LAST_ALLOCATION_DATE,
                    CHG.ISSUE_DATE,
                    CHG.INVOICE_STATUS,
                    GET_LAST_PAYMENT_DATE(CHG.CHARGE_ID)
                FROM
                (
                    SELECT 
                        ENT.ENTITY_NUMBER AS PAYOR_ENTITY_NUMBER,
                        CHG.ID AS CHARGE_ID,
                        CHG.CODE AS CHARGE_CODE,
                        CC.INVOICE_LINE_NUMBER,
                        INV.ID AS INVOICE_ID,
                        INV.INVOICE_NUMBER AS INVOICE_NUMBER,
                        ORG.CODE AS INVOICE_HOSP,
                        ENC.ENCOUNTER_NUMBER AS ENCOUNTER_NUMBER,
                        CHG.SERVICE_START_DATE_TIME,
                        CHG.DESCRIPTION2 AS ENG_DESC,
                        CHG.DESCRIPTION3 AS CHT_DESC,
                        EIS_SPEC.VALUE AS EIS_SPEC,
                        INV.ISSUE_DATE,
                        INV.INVOICE_STATUS,
                        MAX( CASE WHEN O.OPERATION_TYPE = 'A' THEN O.DATE_TIME_ENTERED ELSE NULL END ) AS LAST_ADJUSTMENT_DATE,
                        SUM( CASE WHEN O.OPERATION_TYPE = 'I' THEN OI.AMOUNT ELSE 0 END ) AS CHARGE_AMOUNT,
                        SUM( CASE WHEN O.OPERATION_TYPE = 'A' THEN OI.AMOUNT ELSE 0 END ) AS ADJUSTED_AMOUNT
                    FROM BILLING_SCHEMA.AS_ENCOUNTER ENC 
                    INNER JOIN BILLING_SCHEMA.BD_CHARGE CHG ON CHG.ENCOUNTER_ID = ENC.ID AND CHG.CHARGE_TYPE = 'E' -- ACTUAL CHARGE
                    INNER JOIN BILLING_SCHEMA.BD_CHARGE_COMPONENT CC ON CC.CHARGE_ID = CHG.ID
                    INNER JOIN BILLING_SCHEMA.BD_OPERATION_ITEM OI ON CC.ID = OI.CHARGE_COMPONENT_ID
                    INNER JOIN BILLING_SCHEMA.BD_OPERATION O ON OI.OPERATION_ID = O.ID
                    INNER JOIN BILLING_SCHEMA.AS_ENTITY_DETAIL ENT ON CC.PAYOR_ENTITY_DETAIL_ID = ENT.ID
                    INNER JOIN BILLING_SCHEMA.AS_ENTITY_DETAIL PT ON PT.ID = ENC.RECIPIENT_ENTITY_DETAIL_ID
                    LEFT OUTER JOIN BILLING_SCHEMA.BD_INVOICE INV ON CC.INVOICE_ID = INV.ID
                    LEFT OUTER JOIN BILLING_SCHEMA.OS_ORGANISATION ORG ON INV.ORGANISATIONAL_ID = ORG.ID
                    LEFT OUTER JOIN BILLING_SCHEMA.BD_CHARGE_EXTRA EIS_SPEC ON EIS_SPEC.ENTITY_ID = CHG.ID AND EIS_SPEC.EXTRA_NAME_ID = 9 -- GL EIS Specialty
                    WHERE ENC.ID = N_ENCOUNTER_ID
                    GROUP BY ENT.ENTITY_NUMBER, CHG.ID, CHG.CODE, CC.INVOICE_LINE_NUMBER, INV.ID, INV.INVOICE_NUMBER, ORG.CODE,
                        ENC.ENCOUNTER_NUMBER, CHG.SERVICE_START_DATE_TIME, CHG.DESCRIPTION2, CHG.DESCRIPTION3, EIS_SPEC.VALUE,
                        INV.ISSUE_DATE, INV.INVOICE_STATUS
                )CHG LEFT OUTER JOIN
                (
                    SELECT 
                        CC.CHARGE_ID,
                        CC.INVOICE_LINE_NUMBER,
                        INV.INVOICE_NUMBER,
                        MAX(ALLOC.DATE_TIME_ENTERED) AS LAST_ALLOCATION_DATE,
                        SUM( CASE WHEN TXN.TRANS_TYPE = 'RC' AND TXN.PAYOR_ENTITY_DETAIL_ID = CC.PAYOR_ENTITY_DETAIL_ID THEN TI.AMOUNT * -1 ELSE 0 END ) AS PAID_BY_PAYOR,
                        SUM( CASE WHEN TXN.TRANS_TYPE = 'RC' AND TXN.PAYOR_ENTITY_DETAIL_ID <> CC.PAYOR_ENTITY_DETAIL_ID THEN TI.AMOUNT * -1 ELSE 0 END ) AS PAID_BY_OTHERS,
                        SUM( CASE WHEN TXN.TRANS_TYPE = 'WT' THEN TI.AMOUNT * -1  ELSE 0 END ) AS WAIVED_AMOUNT,
                        SUM( CASE WHEN TXN.TRANS_TYPE IN ( 'CM', 'DM' ) AND ( TS.NAME LIKE '%Perpetual Write-off%' OR RC.NAME IN ('E', 'G' ) ) THEN TI.AMOUNT * -1  ELSE 0 END ) AS WO_OFFSET_AMOUNT,
                        SUM( CASE WHEN TXN.TRANS_TYPE IN ( 'CM', 'DM' ) AND ( TS.NAME NOT LIKE '%Perpetual Write-off%' AND RC.NAME NOT IN ('E', 'G' ) ) THEN TI.AMOUNT * -1  ELSE 0 END ) AS WO_AMOUNT
                    FROM BILLING_SCHEMA.BD_INVOICE INV 
                    INNER JOIN BILLING_SCHEMA.BD_CHARGE_COMPONENT CC ON INV.ID = CC.INVOICE_ID
                    INNER JOIN BILLING_SCHEMA.BD_TRANSACTION_ITEM TI ON CC.ID = TI.CHARGE_COMPONENT_ID
                    INNER JOIN BILLING_SCHEMA.BD_TRANSACTION ALLOC ON TI.TRANSACTION_ID = ALLOC.ID
                    INNER JOIN BILLING_SCHEMA.BD_TRANSACTION TXN ON ALLOC.REF_TRANSACTION_ID = TXN.ID
                    INNER JOIN BILLING_SCHEMA.BD_CHARGE CHG ON CC.CHARGE_ID = CHG.ID
                    LEFT OUTER JOIN BILLING_SCHEMA.REF_TRANSACTION_SUBTYPE TS ON TXN.TRANSACTION_SUBTYPE_ID = TS.ID
                    LEFT OUTER JOIN BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE RC ON TXN.TRANSACTION_REASON_ID = RC.ID
                    WHERE CHG.ENCOUNTER_ID = N_ENCOUNTER_ID
                    GROUP BY CC.CHARGE_ID, CC.INVOICE_LINE_NUMBER, INV.INVOICE_NUMBER
                )ALLOC ON CHG.INVOICE_LINE_NUMBER = ALLOC.INVOICE_LINE_NUMBER AND CHG.INVOICE_NUMBER = ALLOC.INVOICE_NUMBER;
                N_CHARGE_CNT := SQL%ROWCOUNT;
                COMMIT;
            END IF;
            COMMIT;

        END IF; -- IF IS_SKIP = 'N'

        UPDATE PATIENT_ENQUIRY.PADB_ENC_REGEN_CONTROL
        SET COMPLETE_TIME = SYSTIMESTAMP
        WHERE START_TIME = T_START_TIME
        AND   ENCOUNTER_NUMBER = IN_ENCOUNTER_NUMBER;
        COMMIT;

    END IF;
    COMMIT;

    --INSERT INTO ENC_PERFORMANCE
    --SELECT N_ENCOUNTER_ID, IN_ENCOUNTER_NUMBER, T_START_TIME, SYSTIMESTAMP, N_CHARGE_CNT FROM DUAL;
    --COMMIT;

END;