--------------------------------------------------------
--  DDL for Table REF_REFERRAL_SOURCE
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(20 CHAR), 
	"DESCRIPTION" VARCHAR2(100 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_REFERRAL_SOURCE"."NAME" IS 'The unique name of a particular reference table row.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_REFERRAL_SOURCE"."DESCRIPTION" IS 'The description of a referral source type.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_REFERRAL_SOURCE"."ACTIVE_FLAG" IS 'Whether a particular reference table row is active (T = yes, F = no). Only active reference table values will appear in screen combo boxes.';
   COMMENT ON TABLE "BILLING_SCHEMA"."REF_REFERRAL_SOURCE"  IS 'Stores source areas.';
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PATIENT_ENQUIRY";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_REFER_SRC
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_REFER_SRC" ON "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_REFERRAL_SOURCE
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_REFERRAL_SOURCE" BEFORE INSERT ON BILLING_SCHEMA.REF_REFERRAL_SOURCE FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_REFERRAL_SOURCE.NEXTVAL INTO :NEW.ID FROM DUAL; END; 



















/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_REFERRAL_SOURCE" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_REFERRAL_SOURCE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" ADD CONSTRAINT "PK_REF_REFER_SRC" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_REFER_SRC"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_REFERRAL_SOURCE" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);


REM INSERTING into BILLING_SCHEMA.REF_REFERRAL_SOURCE
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (3,'4','OPD','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (4,'5','OTHER HOSP','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (5,'8','NEWBORN INFANT','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (6,'0','OTHER','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (7,'2','OPAS - General/Private Practitioner','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (8,'6','OPAS - IP/DP Discharge from own Hosp','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (9,'7','OPAS - Other','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (10,'9','OPAS - Self-enrolment through Quit line','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (11,'A','OPAS - A-GOPCs own hospital management','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (12,'B','OPAS - B-GOPCs other hospitals management','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (13,'C','OPAS - C-Designated Staff clinics','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (14,'D','OPAS - D-Non-designated staff clinics','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (15,'E','OPAS - E-MCHC','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (16,'F','OPAS - F-EHC','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (17,'G','OPAS - G-Other Services run by DH/Govt Dept','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (18,'1','OPAS - 1-GOP/MCH/Staff clinic/DH & Govt','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (19,'H','OPAS - Allied Health Triage Clinic','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (20,'USER DEFINED','Only the Description has been provided. Please check the user defined field "Referral Source Code".','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (1,'Direct Data Entry','Direct Data Entry','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (2,'3','A&E','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (21,'I','School dental service (SDS)','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (22,'L','Custodial','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (23,'J','Government dental clinic (GDC)','T');
Insert into BILLING_SCHEMA.REF_REFERRAL_SOURCE (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (24,'K','General public session (GP session)','T');
