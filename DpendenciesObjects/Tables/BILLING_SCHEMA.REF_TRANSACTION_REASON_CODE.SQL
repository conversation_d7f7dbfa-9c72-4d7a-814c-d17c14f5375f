--------------------------------------------------------
--  DDL for Table REF_TRANSACTION_REASON_CODE
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(20 CHAR), 
	"DESCRIPTION" VARCHAR2(200 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR), 
	"EXCLUDE_FROM_OUTSTANDING_BAL" VARCHAR2(1 CHAR), 
	"FORCE_INPUT_NOTES" VARCHAR2(1 CHAR)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE"."EXCLUDE_FROM_OUTSTANDING_BAL" IS 'Whether invoices are excluded from the oustanding balance calculation';
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PATIENT_ENQUIRY";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_REASON_CODE
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_REASON_CODE" ON "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_REASON_CODE
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_REASON_CODE" BEFORE INSERT ON BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_REASON_CODE.NEXTVAL INTO :NEW.ID FROM DUAL; END; 



















/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_REASON_CODE" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_TRANSACTION_REASON_CODE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" ADD CONSTRAINT "PK_REF_REASON_CODE" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_REASON_CODE"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" MODIFY ("FORCE_INPUT_NOTES" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_TRANSACTION_REASON_CODE" MODIFY ("EXCLUDE_FROM_OUTSTANDING_BAL" NOT NULL ENABLE);


REM INSERTING into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (799,'00-IA','Incorrect amount','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (800,'00-IM','Incorrect payment means','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (801,'00-IP','Incorrect payee','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (802,'00-DU','Duplicated payment','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (803,'00-CD','Dishonoured cheque','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (804,'00-OT','Other (please specify)','T','T','T');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (1,'A','Deceased Cases','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3,'C','Defaulter''S Address Marked With ''Unknown'' In Hospital Record','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4,'D','Defaulter''S Name Marked With ''Unknown'' In Hospital Record','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (5,'E','Dispute','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (6,'F','Final Warning Issued And Not Returned','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (7,'G','Medico-legal','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (8,'H','Undelivered Mail Returned - Hospital Finance','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (9,'I','Legal Action Cannot Be Taken - Undelivered Mail By Small Claims Tribunal(SCT)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (10,'J','Legal Action Cannot Be Taken - Undelivered Demand Letter By Haho Legal Counsel','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (11,'K','Legal Action Cannot Be Taken - Insufficient Information / Not Cost Effective','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (12,'L','Legal Actions Taken But Not Settled','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (13,'Y','Others-(Must Specify Write-Off Reasons)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (14,'EXP','Expired NEP Obs Package','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2172,'QEH 1051000-00-30283','A Phase 1-2, Open Label Study of The X-Linked Inhibitor of Apoptosis (XIAP) Antisense AEG35156 in Combination with Sorafenib in Patients With Advanced Hepatocellular Carcinoma (HCC) (Protocol No. AEG3','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2173,'QEH 1051000-00-30290','Phase II study of concurrent chemoradiotherapy using IMRT (with SPECT-CT to define functional lung volmme and PET to define GTV) and docetaxel-cisplatin (or carboplatin) followed by adjuvant chempther','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2174,'QEH 1501000-00-30278','A clinical evaluation of the XIENCE V Everolimus Eluting Coronary Stent System in the Treatment of patients with de novo Coronary Artery Lesions  (Protocol No. 05-369)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2175,'QEH 5406000-00-32016','Rental if Physiotherapy Gymnasium at P/2 to HKU Space (12 Oct 11 to 24 Feb 12)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2176,'QEH 1051000-00-30318','Study of BKM120 in patients lung cancer with activated PI3K pathway (Protocol #: CBKM120D2201)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2180,'QEH 1051000-00-30208','Open-label study of Bevacizumab (AVASTIN) plus Taxane Monotherapy (Avastin THErapy for advaNced breAst cancer) (Protocol No. MO19391)-30208','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2181,'QEH 1051000-00-30218','Phase III study comparing the activity of Paclitaxel plus Trastuzumab plus Lapatinib to Paclitaxel plus Trastuzumab plus placebo in women with ErbB2  (Protocol No. EGF104383)-30218','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2182,'QEH 1051000-00-30266','Study of neoadjuvant lapatinib, trastuzumab, and their combination plus paclitaxel in women with HER2/ErbB2 positive primary breast cancer (Protocol No. BIG 1-06/EGF106903)-30266','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2183,'QEH 1051000-00-30285','Comparing First-Line Pemetrexed plus Cisplatin (Followed by Gefitinib as Maintenance)  (Protocol #: H3E-CR-S131)-30285','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2184,'QEH 1051000-00-30306','An open-lable multi-centre study of erlotinib (Tarceva ?) as first line therapy in NSCLC patients who harbor EGFR mutations (Protocol#: ML25637)-30306','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2185,'QEH 1271000-00-64887','Education Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2186,'QEH 1501000-00-30237','Inotuzumab Ozogamicin (CMC-544) administered in Combination with Rituximab compared to a defined Investigator''s choice therapy(Protocol No. 3129K4-3301-WW)-30237','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2187,'QEH 1501000-00-30292','Resolute Asia Study-30292','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2188,'QEH 2261000-00-64873','Training - CTS','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2190,'QEH 5106000-00-64819','Departmental Fund - Dietetics','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2191,'QEH 5206000-00-30037','On-site Training on Occupational Lifestyle Redesign Programme for Retirees for professional staff of HK Housing Society-30037','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2192,'QEH 5206000-00-64820','Departmental Fund - Occ Therapy','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2193,'QEH 5206000-54-39001','ERB PE Recharge_Allowances -Others (Supporting Non Care Related)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2194,'QEH 5406000-00-30041','Clinical Attachment at KCC PT Departments Clinical Placement for Rehab Doctors form China_Shaoxing Peoples Hospital-30041','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2195,'QEH 5456000-00-30020','Clinical Placement to Students from HKU-30020','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2196,'QEH 5456000-00-30038','Training - Project 139 P&O Clinical Attachment as an Observer to QEH-30038','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2197,'QEH 6056000-00-30017','Training (Clinical Placement,Path)  -30017','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2198,'QEH 6316000-00-64818','Departmental Fund - Electro-Diagnostic','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2200,'QEH 6756000-00-30829','Effects of a health-social partnership transitional care model for post-discharged elderly-30829','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2201,'QEH 7657000-00-30803','Library Photocopying charge  -30803','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2202,'QEH 7657000-00-65564','PRC - Departmental Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2203,'QEH 7657001-00-65588','CPRC - Postage','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2204,'QEH 9987000-00-31006','Canteen (Asia Pacific)-31006','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2205,'QEH 9987000-00-32011','Renting out of HA premises : Rehabilitation Shop-32011','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2206,'QEH 1051000-00-30226','A randomized, double-blind, placebo-controlled Phase IIIB trial comparing bevacizumab therapy with or without erlotinib after completion of chemotherapy with bevacizumab for the first-line treatment o','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2207,'BTS 6056007-00-64848','Research and Development Fund for Platelet Immunology Service Project','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2208,'BTS 9987000-00-64916','Blood Transfusion & Related Services','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2209,'BTS 9987000-00-35013','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2210,'BTS 9987000-00-39000','A/P - Temp Receipt for BTS Staff Welfare Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2211,'BTS 9987000-00-00000','Medical Reports and Records','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2212,'BTS 6936000-00-39603','A/P - Temp receipt (BMDR)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2213,'BH 7357000-00-38416','OC Recharges ( Water & Sewage)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2214,'BH 7007000-00-34009','OC Recharges (Pest Control Services)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2215,'HKE 9987000-00-00000','RECOVERY OF SAMARITAN FUND SUBSIDY ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2216,'HKE 1951001-00-64897','DONATION TO HONG KONG EYE HOSPITAL','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2217,'KH 2202200-00-30229','Medical Services (Rehab Dept) - 30229','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2218,'KH 7007000-00-31102','Carpark - 31102','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2219,'KH 7007000-00-38208','Med Chgs-Cert/Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2220,'KH 9987000-00-39614','A/P - Temp (Collection Surplus)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2221,'KH 7007000-00-38208','Recovery of Lost of Staff Card','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2222,'QMH 1003000-00-00000','ANA Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2223,'QMH 1051000-00-64562','HKJCCT Donation - Replacement of a High-energy Linear Accelerator ($20M)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2224,'QMH 1053000-00-00000','COD Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2225,'QMH 1151000-00-64497','Donation - ENT','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2226,'QMH 1271000-00-64514','Donation - Obstetrics & Gynaecology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2227,'QMH 1501000-00-31577','ASOI-UPDATE ON OSTEPOROSIS MGT','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2228,'QMH 1501000-00-64432','Donation - Medicine (for Blood Cancer Patients)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2229,'QMH 1501000-00-64434','Donation - Medicine (Gastroenterology & Gastrointestinal Endoscopy)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2230,'QMH 1601000-00-64407','Research - Genous Stent for Patients Treatment to Coronary Artery Blockage (Med-Cardiology)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2231,'QMH 1951000-00-64510','BMCPC Charity Donation 2008 - Humphrey visual field Analyzer','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2232,'QMH 2001000-00-64541','Donation - Orthopaedics & Traumatology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2233,'QMH 2053000-00-00000','PAE Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2234,'QMH 2151000-00-64576','S.K.Yee Medical Foundation - Establish Parenting Clinic for the Children with Disruptive Behavioural Disorder','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2235,'QMH 2251000-00-64489','S.K.Yee Medical Foundation - Endovascular Aortic Stent Graft Program (1M)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2236,'QMH 3103104-00-00000','OCD GOPC Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2237,'QMH 3113100-00-64599','Research - Safety of combination of valsartan/HCTZ in patient with moderate to severe hypertension','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2238,'QMH 5053000-00-00000','CLP Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2239,'QMH 5206000-00-64655','Donation - Occupational Therapy','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2240,'QMH 5506000-00-64675','Donation - Speech Therapy','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2241,'QMH 6026000-00-64623','Donation - Operating Theatre Services','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2242,'QMH 6116000-00-64635','Donation - Microbiology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2243,'QMH 6256000-00-31586','ASOI-FRCR 2B COURSE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2244,'QMH 7207000-00-38711','LOST OF UNIFORM','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2245,'QMH 7357000-00-31741','Vending Machine Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2246,'QMH 7357000-00-31911','Personal Data (Privacy) Ordiance Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2247,'QMH 7357000-00-38003','Infection Control for Volunteer','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2248,'QMH 7357000-00-64685','S.K.Yee Medical Foundation - Bereavement and Farewell Room','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2249,'QMH 7357000-00-64688','Donation - Equipment Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2250,'QMH 7357000-00-64691','Donation - HCE Office','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2251,'QMH 7657000-00-64721','Donation - PRC Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2252,'QMH 9987000-00-00000','QMH Misc Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2253,'QEH 1051000-00-30264','A randomized, multicenter phase II study to explore whether biomarkers correlate with treatment outcome in chemo-na?ve patients with advanced or recurrent non-squamous non-small cell lung cancer, who ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2254,'QEH 1051000-00-30322','Comparing Orteronel (TAK-700) Plus Prednisone (Protocol no: C21004)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2255,'QEH 9987000-00-00000','Wages in lieu of Notice','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2258,'QEH 1051000-00-30204','Phase 3 Trial of Gemcitabine / Cisplatin Plus PF-3512676 versus Gemcitabine / Cisplatin alone as First line treatment of patients with Advanced non-small cell lung cancer (Protocol No. A8501002)-30204','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2259,'QEH 1051000-00-30214','Asia-Pacific Breast Initiatives 1 (Protocol No. DOCET_R_01832)-30214','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2260,'QEH 1051000-00-30225','Phase III study to assess the efficacy of recMAGE-A3 + AS15 Antigen Specific Cancer Immunotherapeutic as adjuvant therapy(Protocol No. 109493)-30225','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2261,'QEH 1051000-00-30269','Efficacy and Safety of Sorafenib Compared to Placebo in Ovarian Epithelial Cancer or Primary Peritoneal Cancer Patients (Protocol No. BAY 43-9006 / 12007)-30269','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2262,'QEH 1051000-00-30307','Protocol #: EGF 114299 ALapatinib, trastuzumab plus an aromatase inhibitor (AI) versus trastuzumab plus an AI versus lapatinib plus an AI as first-line therapy in postmenopausal subjects -30307','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2263,'QEH 1051000-00-30816','Development of a computational system for design and evaluation of healthcare and fashionable shoes-30816','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2264,'QEH 1051000-00-64859','Lung Cancer Research','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2265,'QEH 1051000-00-64896','Patient Care','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2266,'QEH 1051002-00-61009','Li Ka Shing Foundation Hospice Program (Capital Works)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2267,'QEH 1056000-54-38104','Radiobiology PE Recharge_Allowances- Others (Supporting Non Care Related)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2268,'QEH 1151000-00-64812','Departmental Fund - ENT','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2269,'QEH 1153070-71-38107','Ear Mould (ENT)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2270,'QEH 1271000-00-64885','Departmental Fund - O&G','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2271,'QEH 1501000-00-30258','The e-SELECT Registry: A Multicenter post-market surveillance (Protocol No. E06-01)-30258','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2272,'QEH 2261000-00-65599','Departmental Fund - CTS','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2273,'QEH 5206000-00-30018','Clinical Placement to Students from HKU-30018','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2274,'QEH 5406000-00-32016','Rental if Physiotherapy Gymnasium at P/2 to HKU Space (12 Oct 11 to 24 Feb 12)-32016','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2275,'QEH 6056000-00-30246','Medical Service (Pathology Test)  (1) Chromosomal aberrations in chronic lymphocytic leukaemia by cytogenetic and FISH analyses-30246','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2276,'QEH 6056000-00-30274','The application of Interferon gamma release assay in the assessment of Latent Tuberculosis Infection (LTBI) among healthcare workers in Hong Kong. (Protocol No. KCC-ICT-RP-2008-01) -30274','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2277,'QEH 6056000-00-64843','Departmental Fund - Pathology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2278,'QEH 7657000-00-65565','PRC - Brain Group','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2279,'QEH 7657000-00-65571','PRC - Volunteer service (subv)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2280,'QEH 7657001-00-65581','CPRC - Departmental Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2281,'QEH 7657001-00-65586','CPRC -Printing & Stationery','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2282,'QEH 7657001-00-65592','CPRC - Service Ambassador Honorarium (HKCF)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2283,'QEH 7857000-00-30818','Library Services (School of General Nursing)-30818','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2284,'QEH 7857000-00-32010','Hiring of Lecture Theatre (School of General Nursing)-32010','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2285,'QEH 9987000-00-30244','Chinese Medicine Clinic - PE of MO-30244','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2286,'QEH 9987000-00-30901','Personal Data Privacy Ordinance (Personal Record)-30901','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2287,'QEH 9987000-00-30902','Personal Data Access Request Tracking System (PDARTS) (Med Report)-30902','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2288,'QEH 9987000-00-31105','Car Park-31105','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2289,'QEH 9987000-00-32015','Study of post-operative adjuvant Lapatinib or placebo and concurrent chemoradiotherapy followed by maintenance Lapatinib or placebo monotherapy in high-risk subjects MDSSC_Rental out of premises-32015','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2290,'QEH 9987005-00-39000','KCC SWF-INTEREST GROUP','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2291,'QEH 9987006-00-39000','KCC SWF','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2292,'QEH 6256000-00-64823','Scientific Assembly & Annual Meeting of Radiological Society of North American','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2293,'QEH 2051000-00-65602','Training - Course Fees & Conference - Paediatrics','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2294,'QEH 1051000-00-30211','A phase III, randomized, double-blind, multi-centre, parallel-group study to assess the efficacy of ZD6474 (ZAXTIMATM) versus Erlotinib (TARCEVA) in patients with locally advanced or Metastatic (Stage','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2295,'QEH 1051000-00-30227','A Phase 2, Randomized trial of Chemoradiation with or without Panitumumab in Subjects with Unresected, Locally Advanced Squamous Cell Carcinoma of the Head and Neck (Protocol No. 20062080) (KC/KE-08-0','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2296,'BTS 6056000-00-38114','OC Recharge -Other Expenses','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2297,'BTS 6166000-00-38304','OC Recharge -Other Expenses','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2298,'BTS 9987000-00-64904','Foundation Lecture Endowment Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2299,'BTS 9987000-00-64905','Glorious Sun Foundation Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2300,'BTS 9987000-00-64938','BTS 60th Anniversary Activities','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2301,'BTS 6936000-00-65709','General Operation of the Registry for HKBMDR','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2302,'BTS 9987000-00-00000','Miscellaneous Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2303,'BTS 9987000-00-00000','Proceeds from disposal of Fixed Assets','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2304,'BTS 9987000-00-00000','Deposit - Others','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2305,'BH 7357000-00-38202','OC Recharges (HCE)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2306,'HKE 7007000-00-38416','ELECTRICITY - TUCKSHOP','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2307,'HKE 9987000-00-31704','ASOI - VENDING MACHINE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2308,'HKE 9987000-00-00000','MONTHLY LICENCE FEE - KIOSK SERVICE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2309,'HKE 9987000-00-00000','PROCEEDS FROM DISPOSALS OF FIXED ASSETS','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2315,'KH 7707000-00-38209','Proceeds from Disposal of Fixed Assests','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2316,'QMH 1401000-00-64549','Donation - Intensive Care Unit (CTSD)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2317,'QMH 1501000-00-38040','Diabetes Education Management Course','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2318,'QMH 1501000-00-64430','S.K.Yee Medical Foundation - Provision of Oral Arsenic Free of Charge to Patients for the Treatment of Blood Cancers','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2319,'QMH 1501000-00-64450','Reduction of Atherosclerotic Plaque Burden in Native and Intervened Coronary Arteries & in Other Major Arteries by High Dose Atorvastatin Therapy (EC 1607-01)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2320,'QMH 1501000-00-64453','A Randomised, Double-blind, Placebo Controlled, Multicentre, Phase III Study of Tarceva plus Chemotherapy vesus Chemotherapy Alone in Patients with Non-small Cell Lung Cancer','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2321,'QMH 1501022-00-64406','Medicine (BMT) - Relocation of Bone Marrow Bank','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2322,'QMH 1801000-00-64741','Donation - JSJ Fund for NICU & O&G','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2323,'QMH 2051000-00-64523','Donation for Paediatric Cancer & BMT Patients (ATV & HK Today''s Charitable Fund)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2324,'QMH 2051000-00-64526','S K Yee Medical Foundation - Paediatrics (1.43M)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2325,'QMH 2151000-00-64575','Donation - Psychiatry','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2326,'QMH 2153000-00-00000','PSY Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2327,'QMH 2251000-00-64491','S.K.Yee Medical Foundation - Endovascular Aortic Stent Graft Program-a minimally invasive($640,000)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2328,'QMH 2261000-00-64472','Donation - Cardiothoracic Surgery','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2329,'QMH 5056000-00-64645','Donation - Clinical Psychology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2330,'QMH 5206000-00-64656','Research - A Community OT falls reduction programme for elderly attending A&E because of fall','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2331,'QMH 5353000-00-00000','POD Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2332,'QMH 5906000-00-64660','Donation - Pharmacy','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2333,'QMH 6056000-00-64629','Donation - Pathology / Haematology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2334,'QMH 7357000-00-31913','ASOI PDPD TWH/GOPCs','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2335,'QMH 7357000-00-64708','Donation - Transplant Centre','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2336,'QMH 9987000-00-35013','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2337,'QEH 6756000-00-30289','Health-social partnership transitional care model for post-discharged elderly','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2338,'QEH 1401000-00-30315','ARISE Study_ in Australasia (ICU)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2339,'QEH 1051000-00-30319','Trastuzuman as adjuvant therapy in patients with operable Her2-Positive Early Breast Cancer (Protocol no: MO28048)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2340,'QEH 1501000-00-30320','The ENCORE 1 Study: Efavirenz (EFV) with standard dose EFVplus two nucleotide reverse transcriptase inhibitors (N(t)RTI) in antiretroviral-na?ve HIV-infected individuals over 96 weeks.','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2342,'QEH 9987000-00-39614','Pharm Recharge','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2343,'QEH 2001000-00-38111','Sales/Hire Charges (O&T)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2344,'QEH 6256000-00-38106','Disposal of Used X-ray (DRI)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2345,'QEH 7027000-00-30820','Deposit (HA Library)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2348,'QEH 1051000-00-30201','Phase II maker identification trial for non-small cell lung cancer patients treated with Tarceva as second line therapy (Protocol No. BO18279)-30201','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2349,'QEH 1051000-00-30205','Phase III study of Lapatinib (GW572016) in combination with Paclitaxel plus Placebo in subjects with ErbB2 Amplified metastatic breast cancer (Protocol No. EGF 104535)-30205','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2350,'QEH 1051000-00-30216','Clinical Trial - Sunitinib Malate (SU 011248) or Capecitabine (Protocol No. A6181107)-30216','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2351,'QEH 1051000-00-30224','Safety and efficacy of Betamare plus chemotherapy to chemotherapy alone in the prevention and treatment of cancer Anorexia and Cachexia syndrome -30224','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2352,'QEH 1051000-00-30281','NPC Tissue Bank - Area of Excellence (AoE) - Center for Nasopharyngeal Carcinoma Research-30281','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2353,'QEH 1051000-00-30284','Asia Pacific Breast Initiatives II  (Protocol #: DOCET-R-04470)-30284','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2354,'QEH 1051000-00-30293','Combination trastuzumab and capecitabine, in patients with HER2-positive metastatic breast cancer (PHEREXA)-30293','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2355,'QEH 1501000-00-30233','Enhanced Home & Community Care Services *-30233','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2356,'QEH 1501000-00-64876','Departmental Fund - Medicine','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2357,'QEH 1501000-00-64878','Renal Team','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2358,'QEH 1501000-00-64880','Clinical Research Project (Renal)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2359,'QEH 2051000-00-30028','Clinical Attachment for Diploma in Child Health DCH (Syd.)-30028','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2360,'QEH 3203200-00-64924','Training - Course Fees & Conference - A&E','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2361,'QEH 5406000-00-30019','Training (PT Clinical Placement)-30019','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2362,'QEH 5406000-00-30035','Kunming Medical University Student Placement 10/11-30035','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2363,'QEH 5906000-00-64866','Departmental Fund - Pharmacy','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2364,'QEH 6756000-00-64821','Departmental Fund - Nursing','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2365,'QEH 7357000-00-30822','Air-conditioning (Nurse Quarter)  -30822','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2366,'QEH 7507000-00-30824','HA Research Ethics Committee (HA REC)-30824','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2367,'QEH 7657000-00-65578','PRC - Sundries & Miscel','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2368,'QEH 7657001-00-65589','CPRC - Sundries & Miscel','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2369,'QEH 9987000-00-00000','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2370,'QEH 9987000-00-31001','Cafeteria (Pacific Coffee)-31001','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2371,'QEH 9987004-00-39000','KCC SWF-CARNIVAL GROUP','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2372,'BTS 7557000-00-38302','OC Recharge -Other Expenses','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2374,'BTS 6176000-00-64918','Research and Development Fund for Skin Disinfection Study','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2375,'BTS 7007000-00-38208','Medical Equipment','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2376,'BTS 9987000-00-00000','Sales / Hire Charges (Medical and Dental Equipment)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2377,'BH 9987000-00-31705','ASOI Income (Vending Machine)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2378,'BH 9987000-00-65708','Donation Box','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2379,'BH 7357000-00-38202','Medical Report & Records','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2380,'BH 9987000-54-00000','PE Recharge','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2381,'KH 9987000-00-39614','Medical Equipment','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2382,'KH 9987000-00-39000','Staff Recreation Activities','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2385,'KH 6756000-00-30007','CND Training - 30007','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2386,'KH 7007000-00-30807','Other - Location Filming - 30807','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2387,'KH 5206000-00-31402','OT - Outward Scheme -31402','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2388,'KH 5403000-00-32004','Renting Out of HA Premises - Rental of PT Hydrotherapy Pool - 32004','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2389,'KH 9987000-00-39614','Interest Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2390,'KH 7007000-00-38208','Other Deposit','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2392,'KH 7007000-00-38416','Recharge Electricity ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2393,'QMH 1051000-00-64555','Donation - Cancer Patients/ Relative Support Programme (In memory of Mr. Paul Wong)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2394,'QMH 1253000-00-00000','GYN Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2395,'QMH 1271000-00-38038','Prenatal Diag Service in TYH','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2396,'QMH 1271000-00-38045','Deposit for preimplant genetic diagnosis','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2397,'QMH 1271001-00-38018','Deposit-Breast Pump','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2398,'QMH 1501000-00-38032','Cytogenetics, HKU','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2399,'QMH 1501000-00-38047','Update on diabetes MGT course','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2400,'QMH 1501000-00-64417','S K Yee Medical Foundation - Synchronous Biventricular Pacing','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2401,'QMH 1501000-00-64422','Study of Lamivudine for Prevention of HBV Reactivation in HBsAg Seropositive Patients undergoing Cytotoxic Chemotherapy','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2402,'QMH 1501000-00-64451','Use of Attain OTW Model 4193 left ventricular pacing wire for biventricular pacing in heart failure patients (EC 1571-01)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2403,'QMH 1851000-00-64504','S.K.Yee Medical Foundation Grant 2010 - Neurovascular Intervention Program','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2404,'QMH 1853000-00-00000','NEU Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2405,'QMH 1953000-00-00000','OPH Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2406,'QMH 2051000-00-64525','Donation - Research in Childhood Leukaemia (Paediatrics)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2407,'QMH 2051000-00-64528','S.K.Yee Medical Foundation - Provision of Universal Newborn Hearing Screening (Paediatrics)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2408,'QMH 3553400-00-00000','IC Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2409,'QMH 5106000-00-64650','Donation - Dietetics','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2410,'QMH 5203000-00-00000','OCC Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2411,'QMH 5453000-00-00000','P&O Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2412,'QMH 6056000-00-64630','Donation - Pathology / Clinical Biochemistry','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2413,'QMH 6256000-00-00000','Misc income-Discount of 40% on CT320','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2414,'QMH 7357000-00-31583','ASOI-SAP','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2415,'QMH 7357000-00-31883','ASOI-OTHERS','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2416,'QMH 7357000-00-38002','Staff Welfare Fund-HKWC','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2417,'QMH 7357000-00-38005','HKW Sports Association','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2418,'QMH 7357000-00-64701','Training & Research Assistance Scheme','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2419,'QMH 7357000-00-64702','Poon Hung Chiu Training Fellowship','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2420,'QMH 7357000-00-64707','Donation - Staff Welfare Fund (SARS)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2421,'QMH 9987000-00-00000','Auction Sale','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2422,'QEH 1501000-00-30270','A Prospective, Randomized, Double-Blind, Double-Dummy, Parallel-Group, Multicenter, Event-Driven, Non-Inferiority Study Comparing the Efficacy and Safety of Once-Daily Oral Rivaroxaban (BAY 59-7939) w','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2423,'QEH 1501000-00-30286','A phase II single arm, multi-centre study Bevacizumab (Avastin?) pre- and post-transarterial chemoembolization (TACE) treatment for localised unresectable Hepatocellular Carcinoma (HCC) (Protocol No. ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2424,'QEH 9987000-00-30823','QEH Neck Cord & Strap','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2425,'QEH 1051000-00-30317','INSPIRE – Stimuvax trial In Asian NSCLC Patients: stimulating Immune REsponse  (Study Protocol No.: EMR 63325-012)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2427,'QEH 5456000-00-38005','Sales/Hire Charges (P&O)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2428,'QEH 1501000-00-38108','Miscellaneous Income (Medical)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2429,'QEH 9987000-00-00000','Deposit (Contractor)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2434,'BTS 6056000-54-38114','PE Recharge_Allowances- Others','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2435,'QEH 1051000-00-30202','An expanded access program of Tarceva TM (erlotinib) in patients with advanced stage IIIB/IV non-small cell lung cancer (Protocol No. MO18109)-30202','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2436,'QEH 1051000-00-30213','Efficacy and tolerability of Aprepaitant for the prevention of chemotherapy-induced and vomiting associated with moderately emetogenic chemotherapy (Protocol No.: 130-00)-30213','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2437,'QEH 1051000-00-30219','Randomized trial of AMG 706 or Bevacizumab in combination with Paclitaxel and Carboplatin for advanced non-squamous non-small cell lung cancer (Protocol No. 20060136)-30219','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2438,'QEH 1051000-00-30228','Concomitant radiation, cisplatin, and tirapazamine (SR259075) versus concomitant radiation and cisplatin in patients with advanced head and neck cancer (Protocol No. EFC5512)-30228','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2439,'QEH 1051000-00-30239','Adjuvant lapatinib, trastuzumab, their sequence and their combination in patients with HER2/ErbB2positive primary breast cancer   (Protocol No. BIG 2-06/N06D/EGF106708)-30239','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2440,'QEH 1051000-00-30262','Phase III Trail of CP-751,871 in Combination with Paclitaxel and Carboplatin versus Paclitaxel and Carboplatin in patients with non-small cell lung cancer. Protocol No: A4021016-30262','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2441,'QEH 1051000-00-30262','Trial of CP-751,871 in combination with paclitaxel and carboplatin versus paclitaxel and carboplatin in patients with non-small cell lung cancer (Protocol No. A-4021016)-30262','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2442,'QEH 1051000-00-30263','Multicentre study of Alpharadin in the treatment of patients with symptomatic hormone refractory prostate cancer with skeletal metastases (Protocol No. BC1-06)-30263','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2443,'QEH 1051000-00-30272','Bevacizumab plus capecitabine vs. bevacizumab alone as maintenance therapy (Protocol No. MO22223)-30272','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2444,'QEH 1051000-00-30277','Efficacy and safety of RAD001 as first-line followed by second-line sunitinib versus sunitinib as first-line followed by second-line RAD001 (Protocol No. CRAD001L2202)-30277','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2445,'QEH 1051000-00-30298','A molecular epidemiology study in Asia patients with advanced NSCLC of adeno histology to assess EGFR mutation status: PIONEER study  (Protocol #: D7913L00086) -30298','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2446,'QEH 1051002-00-61010','Li Ka Shing Foundation Hospice Program (Expenditure)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2447,'QEH 1271000-00-30044','Clinical Attachment from Nanfang Hospital-30044','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2448,'QEH 1271000-00-30295','Effect of ear acupressure on acute postpartum perineal wound pain: a randomized controlled study-30295','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2449,'QEH 1501000-00-30248','(A) multi-center, single-arm study of the (T)AXUS (L)iberte-SR Stent for the treatment of patients with De Novo Coronary (A)rtery Lesion(S)-30248','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2450,'QEH 1501000-00-30254','Global Taxus liberte registry program to support worldwide commercialization (OLYMPIA)-30254','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2451,'QEH 1501000-00-30257','SPF 012: Pilot study on involving HIV infected MSM in secondary prevention through group intervention  (** Note: this ASOI s/h report to Government. Balance must be carried forward)-30257','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2452,'QEH 1501000-00-30296','An international observational study to characterize adults who are hospitalized with complications of influenza A-Pandemic H1N1 (H1N1v): FLU003-30296','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2453,'QEH 1501000-00-30299','Ritonavir boosted lopinavir and 2-3N(t) RTI backbone versus ritonavir boosted lopinavir and raltegravir in participants failing first-line NNRT1/2N(t) RTI therapy : the SECOND-LINE study -30299','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2454,'QEH 2251000-00-64829','Setting up an Endovascular Operation Room granted by Hong Kong Jockey Club','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2455,'QEH 3553400-00-30304','Secondary prevention of stroke: Knowledge and perception of stroke risk factors among primary care HK Chinese patients with previous history of stroke or TIA-30304','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2456,'QEH 5206000-00-30016','Training for non-KCC HA staff and non-HA professionals-30016','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2457,'QEH 5406000-00-30031','Sichuan Western Hospital Student Placement 10/11-30031','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2458,'QEH 5406000-00-32012','Physiothreaphy Gym. rental-32012','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2460,'QEH 6006000-00-30036','To Serve as instructor/speaker in the Sedation Training Workshop organized by the HK Academy of Medicine and HK College of Anaesthesiologists-30036','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2461,'QEH 6256000-00-64817','Departmental Fund - DRI','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2462,'QEH 6606000-00-30022','China Delegates Visit-30022','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2463,'QEH 7357000-00-64830','Departmental Fund (Cancer Research Committee)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2464,'QEH 7657000-00-65568','PRC - Renal Support Group','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2465,'QEH 7657000-00-65569','PRC - Education Programs','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2466,'QEH 7657000-00-65596','PRC - Ward visit volunteer GP (subv)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2467,'QEH 9987000-00-39614','Miscellous Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2468,'QEH 9987008-00-39000','KCC SWF-OTHERS','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2469,'BTS 7007000-00-38208','OC Recharge -Other Expenses','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2470,'BTS 6056013-00-64915','The Evertrue Family Trust - Upgrading of Cord Blood Bank Facilities and Increase of Cord Blood Inventory','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2471,'BH 9987000-00-30905','ASOI Income (PDPO)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2472,'BH 7357000-00-38202','Quarter - Utilities Charges','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2473,'HKE 7007000-00-38208','LOSS / DAMAGES OF SECURITY ACCESS CARD','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2474,'HKE 9987000-00-00000','MEDICAL REPORT FEE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2475,'HKE 6946000-00-30828','ASOI-OTHERS:EYE TISSUE HANDLING FEES','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2476,'HKE 6756000-00-30014','ASOI - TRAINING','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2477,'HKE 9987000-00-64800','DONATION FOR HOSPITAL OPERATING EXPENSE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2478,'HKE 6946000-00-64923','DONATION FOR EYE BANK','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2482,'KH 9987000-00-00000','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2483,'KH 7507000-00-32007','Renting Out of HA Premises - Promotion Counter - 32007','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2484,'KH 7707000-00-38208','Contractor Deposit','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2485,'KH 7007000-00-38208','Car Park Deposit','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2487,'QMH 1051000-00-61010','Donation - Li Ka Shing Foundation Hospice Program (Phase I)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2488,'QMH 1103000-00-00000','OMD Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2489,'QMH 1271000-00-38035','Assisted Reproduction Programme-HKU','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2490,'QMH 1271000-00-38039','Receipt for WDTC','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2491,'QMH 1273001-00-00000','HKU-O&G(CR)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2492,'QMH 1501000-00-64402','Donation - Medicine (BMT)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2493,'QMH 1501000-00-64403','Donation - Medicine (Haematology)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2494,'QMH 1501000-00-64413','Donation from HKU - Central Cardiac Monitors & Telemetry System','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2495,'QMH 1501000-00-64440','Donation - Ho Ting Sik Sleep Disorders Centre','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2496,'QMH 1501000-00-64505','Provision of Financial Assistance for Haematology Patients','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2497,'QMH 1501022-00-00000','HKU-J8S(CR)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2498,'QMH 1851000-00-64503','Donation - Neurosurgery','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2499,'QMH 2003000-00-00000','O&T Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2500,'QMH 2051000-00-64521','Donation - Paediatrics & Adolescent Medicine','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2501,'QMH 2251000-00-64475','Donation - Surgery (Colorectal Surgery)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2502,'QMH 2251000-00-64483','Donation - Day Surgery Centre','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2503,'QMH 3113100-00-64601','Research - RSV Study','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2504,'QMH 6456000-00-64738','Donation - Central Sterile Supply','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2505,'QMH 7007000-00-64728','Donation - Minimal Invasive Surgery Centre','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2506,'QMH 7357000-00-64709','Donation - HKW Cluster (Sports Association)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2507,'QEH 1051000-00-30297','A randomized, double blind, placebo controlled, multicenter Phase III trial of bevacizumab, temozolomide and radiotherapy, followed by bevacizumab and temozolomide versus placebo, temozolomide and rad','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2508,'QEH 1501000-00-30261','A Randomised Open-Label Study Comparing the Safety and Efficacy of Three Different Combination Antiretroviral Regiments as Initial Therapy for HIV Infection','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2509,'QEH 1051000-00-30313','Registry of treatment patterns in patients with metastatic castration-resistant prostate cancer (mCRPC) (Study #: DIREG_C_05765)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2510,'QEH 6006000-00-30314','Study of  pregabalin in the treatment of patients with postherpetic neuralgia (Protocol # A0081224)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2511,'QEH 9987000-00-39614','Outside Work','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2512,'QEH 9987000-00-00000','Deposit (Rental)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2516,'BTS 6176000-54-38303','PE Recharge_Allowances- Others','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2517,'BTS 7557000-54-38302','PE Recharge_Allowances- Others','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2518,'QEH 1051000-00-30207','Open-label study of bevacizumab (AVASTIN) in combination with platinum-containing chemotherapy as first-line treatment (SAIL-Safety of Avastin in Lung) Protocol No. MO19390-30207','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2519,'QEH 1051000-00-30212','Efficacy of ZD6474 in Patients with locally Advanced or Metastatic (Stage  IIIB-IV) Non-small Cell Lung Cancer (NSCLC) {Study Protocol No.: D4200C00044}-30212','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2520,'QEH 1051000-00-30275','Efficacy and Safety of Pazopanib Monotherapy Versus Placebo in Women Who Have not Progressed after First Line Chemotherapy (Protocol No. VEG110655) -30275','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2521,'QEH 1051000-00-30302','Addition of bevacizumab to carboplatin and paclitaxel as front-line treatment of epithelial ovarian cancer, fallopian tube carcinoma or primary peritoneal carcinoma (Protocol no: M022923)-30302','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2522,'QEH 1051000-00-30303','Protocol#: 20060540: Trail of ANG 479 or Placebo in Combination with Gencitabine as First-line therapy for metastatic Adnocarcinoma of the Pancreas-30303','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2523,'QEH 1051000-00-30305','Ramucirumab (IMC-1121B) drug product and Best Supportive Care (BSC) vesus placebo and BSC as second-line treatment (Protocol#: IMCL CP12-0919)-30305','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2524,'QEH 1051000-00-30310','Phase 3, randomized, open-label study of the efficacy and safety of crizotinib versus pemetrexed/ cisplatin or pemetrexed/ carboplatin in previously untreated patients (Protocol #: A 8081014)-30310','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2525,'QEH 1051000-00-30311','Safety and efficacy of Darbepoetin Alfa administered at 500mg Once-Every-3-Weeks in Anemic subjects (Study Protocol No.: 20070782)-30311','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2526,'QEH 1051000-00-64856','Breast Cancer Research','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2527,'QEH 1051000-00-64858','For Clinical Research','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2528,'QEH 1153070-00-30015','Clinical placement training to the Speech Therapy students of Hong Kong University.-30015','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2529,'QEH 1501000-00-30238','Patient Related Outcomes with Endeavor versus Cypher stenting Trial registry-30238','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2530,'QEH 1501000-00-30245','TREAT Asia HIV Observational Database-30245','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2531,'QEH 1501000-00-30252','E Five Registry - A prospective multi-center registry to evaluate the real world clinical performance of the Medtronic Endeavor ? ABT-578 eluting coronary stent system-30252','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2532,'QEH 1501000-00-30253','A triple - blinded,randomised,placebo-controlled trail to examineb the efficacy and safety of ViNeuro in patients with Parkinson, s disease.-30253','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2533,'QEH 1501000-00-30255','Study Protocol No. CRAD001A2309: Efficacy and safety comparing concentration-controlled Certican in two doses (1.5 and 3.0 mg/day starting doses) -30255','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2534,'QEH 1501000-00-30271','Study Of Pregabalin (Lyrica) And Lamotrigine (Lamictal) In Patients With Newly Diagnosed Partial Seizures.(Protocol No. A0081046)-30271','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2535,'QEH 1501000-00-30282','Efficacy and Safety of E2007 (Perampanel) Given as Adjunctive Therapy in Subjects with Refractory Partial Seizures (Protocol No. E2007-G000-306)-30282','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2536,'QEH 1501000-00-30288','HIV Resistance Project MSS 177R Surveillance and monitoring of HIV drug resistance in Hong Kong-30288','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2537,'QEH 1851000-00-64884','QEH Neurosurgical Staff Education Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2538,'QEH 2001000-00-64890','Departmental Fund - O&T','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2540,'QEH 5206000-00-30029','Occupational Therapy Clinical Attachment to QEH-30029','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2541,'QEH 6556060-00-64909','Departmental Fund - ACC','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2542,'QEH 6756000-00-30021','Clinical observation (School of General Nursing)-30021','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2543,'QEH 6876000-00-31101','Car Park - B Zone-31101','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2545,'QEH 7357000-00-30821','Air-Conditioning(Interns Quarter)  -30821','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2546,'QEH 7657000-00-30802','Library Penalty  -30802','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2547,'QEH 7657000-00-65573','PRC - Publication (subv)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2548,'QEH 7657000-00-65574','PRC - Honorarium-Volunteer Clerk (exp)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2549,'QEH 7657000-00-65598','PRC - Diabetic (Adult)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2550,'QEH 7657001-00-65798','CPRC - Donation Box','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2551,'QEH 7857000-00-30034','Nursing Program-30034','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2552,'QEH 7907000-00-32014','Rental Income from the HKMA Orchestra-32014','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2553,'QEH 9987000-00-32002','Renting out of HA premises : Chinese Medicine Clinic-32002','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2554,'QEH 9987003-00-39000','KCC SWF-SPORTS / GAMES GROUP','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2555,'QEH 1051000-00-64854','Research in Head & Neck Cancer','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2556,'QEH 1051000-00-30241','A phase II, Dose-Escalation to rash Tail of Erlotinib ( Tarceva) plus Gemcitabine in patients with Metastatic Pancreatic Cancer. (Protocol No. BO 21128 Ref: KC/KE 08-0035/ER-1) ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2557,'BTS 6056000-00-30815','Sales of Blood Derivatives to Non-HA Hospitals','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2558,'BTS 6936000-00-64906','Support of Operation in particular HLA Testing of HKBMDR','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2559,'BTS 6176022-00-64911','Blood Collection Section','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2560,'BTS 9987000-00-00000','Recovery of lost of staff card','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2561,'BTS 6056000-00-38114','Laboratory Supplies','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2562,'BTS 9987000-00-00000','Interest Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2563,'BH 7357000-00-38202','Misc. Income (Rental from Funeral Hall)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2564,'HKE 9987000-54-00000','PE RECHARGE ACCOUNT','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2565,'HKE 7007000-00-38208','IDD CHARGES','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2566,'HKE 9987000-00-00000','RENTAL DEPOSIT FOR KIOSK','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2567,'HKE 1951001-00-64897','THE SIR ROBERT HO TUNG CHARITABLE FUND','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2568,'KH 6756000-00-38211','Apply for Nursing Transcript ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2569,'KH 9987000-00-39614','Exchange Gain','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2570,'KH 9987000-00-39614','A/P - Others','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2574,'KH 5206000-00-30040','OT VR Training Software - 30040','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2575,'KH 7007000-00-31702','Vending Machine - 31702','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2576,'KH 7357000-00-32013','Renting Out of HA Premises - Leasing of B2 to HKU - 32013','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2577,'KH 9987000-00-39614','OC Recharge - Other Expenses','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2579,'KH 5403000-00-38007','Rental of  PT Hydrotherapy Pool to PGs / NGOs','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2580,'QMH 1271000-00-64515','Donation - AIDS Trust Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2581,'QMH 1271000-00-64516','Donation - Obstetrics (TYH)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2582,'QMH 1271001-00-38671','Rental-Breast Pump','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2583,'QMH 1273015-00-00000','HKU-TYH-PDC(CR)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2584,'QMH 1401000-00-64548','DONATION - AICU','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2585,'QMH 1501000-00-31887','ASOI-IRB','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2586,'QMH 1501000-00-64404','Donation - Medicine (Renal)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2587,'QMH 1501000-00-64405','Donation - Medicine (Cardiology)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2588,'QMH 1501000-00-64439','S.K.Yee Medical Foundation - Wireless Capsule Endoscopy','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2589,'QMH 1501000-00-64441','S.K.Yee Medical Foundation - Use of sevelamer hydrochloride to control hyperphosphatemia and reduce calcification burden in the poor peritoneal dialysis patients','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2590,'QMH 1501000-00-64454','Donation - Satellite Diabetes Centre','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2591,'QMH 1951000-00-64509','Donation - Ophthalmology (Wu Jieh Yee Charitable Foundation)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2592,'QMH 2001000-00-64542','Donation - Paraplegic Walking Centre ( O&T )','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2593,'QMH 2051000-00-64531','Donation - Paediatrics & Adolescent Medicine (TYH)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2594,'QMH 2251000-00-64474','Donation - Surgery (Paediatric Surgery)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2595,'QMH 2251000-00-64488','S.K.Yee Medical Foundation - Endovascular Aortic Stent Graft Program','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2596,'QMH 2251000-00-64493','S.K.Yee Medical Foundation - Endovascular Aortic Stent Graft Program-a minimally invasive ($704,000)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2597,'QMH 2253000-00-00000','SRG Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2598,'QMH 2261000-00-64222','Cardiothoracic Surgery - Artificial Heart & Ventricular Assist Device for End Stage Heart Failure Patients','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2599,'QMH 2263000-00-00000','CTSD Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2600,'QMH 3103101-00-00000','AJC GOPC Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2601,'QMH 3103103-00-00000','KJC GOPC Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2602,'QMH 3113100-00-64600','Research - Clinical Study of Uncomplicated Acute Influenza','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2603,'QMH 3203200-00-00000','A&E Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2604,'QMH 5206000-00-64657','Donation - Production of Audio Visual Educational Materials','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2605,'QMH 5403000-00-00000','PHY Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2606,'QMH 5456000-00-64670','Donation - Prosthetic & Orthotic','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2607,'QMH 6256001-00-31579','ASOI-EI COURSE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2608,'QMH 6756000-00-64681','Donation - QMH Nurses Alumni Engagement Committee','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2609,'QMH 7357000-00-31683','ASOI-MEDICAL SERVICE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2610,'QMH 7357000-00-38001','Staff Welfare Fund-QMH','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2611,'QMH 7357000-00-64689','Donation - Purchase of Nesbit Evans Contoura 460 electric hospital beds','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2612,'QMH 7357000-00-64690','Donation - Trusted Brands','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2613,'QMH 7357000-00-64706','Donation - HKW Cluster','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2615,'QEH 1051000-00-30268','A Multinational, Randomized, Open-Label, Phase 3 Study of Sunitinib Malate versus Sorafenib in Patients with Advanced Hepatocellular Carcinoma (Protocol No. A6181170)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2616,'QEH 1501000-00-30235','Prevention of cerebrovascular and cardiovascular Events of ischaemic origin with teRutroban in patients with a history oF ischaemic strOke or tRansient ischaeMic attack The PERFORM Study','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2617,'QEH 9987000-00-39614','Over 6 months unclaimed cheque','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2620,'BTS 7007000-54-38208','PE Recharge_Allowances- Others','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2621,'QEH 1051000-00-30217','(SU011248) Continuation Protocol for patients who have completed a prior Sunitinib study and have the potential to benefit from Sunitinib Treatment (Protocol No. A6181114)-30217','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2622,'QEH 1051000-00-30300','A Population Based Study on Breast Cancer Epidemiology in Hong Kong-30300','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2623,'QEH 1051000-00-30312','Phase 3 trial comparing the efficacy of Ipilimumab in addition to Paclitaxel and Carboplatin versus placebo in addition to Paclitaxel and Carboplatin (NSCLC) (Study Protocol No.: CA184104)-30312','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2624,'QEH 1271000-00-64888','Department''s medical academic activities','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2625,'QEH 1401000-00-64816','Departmental Fund - ICU','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2626,'QEH 1501000-00-30259','Evaluate the Lipid-Altering Efficacy and Safety of MK-0524B Combination Tablet compared to MK-0524A + Simvastatin Coadministration in Patients with Primary Hypercholesterolemia and Mix-30259','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2627,'QEH 7857000-00-38223','Crutch Deposit -Others (Nursing School)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2628,'QEH 2251000-00-64840','Departmental Fund - Surgery','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2630,'QEH 3203200-00-64899','Departmental Fund - A&E','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2631,'QEH 5456000-00-30025','P&O Clinical Attachment as an Observer to QEH-30025','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2632,'QEH 6006000-00-64822','Departmental Fund - Anaesthesia','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2633,'QEH 6256000-00-64875','Training - DRI','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2634,'QEH 7027000-00-30820','Library Services (M4)  -30820','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2635,'QEH 7357000-00-65701','QEH - General Donation Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2636,'QEH 7607000-00-64831','QEH Satellite Oasis - Wellspring','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2637,'QEH 7657000-00-65575','PRC - Printing & Stationery (exp)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2638,'QEH 7657000-00-65577','PRC - Furniture & Equipment','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2639,'QEH 7657000-00-65579','PRC - New Patient Group','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2640,'QEH 7657001-00-65582','CPRC - SWA (exp)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2641,'QEH 7657001-00-65591','CPRC - GSA (exp)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2642,'QEH 9987000-00-00000','Collection Surplus A/P - Temp','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2643,'QEH 9987000-00-31002','Canteen (Hang Fai)-31002','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2644,'QEH 9987000-00-31701','Vending Machine-31701','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2645,'QEH 9987000-00-32003','Renting out of HA premises : Promotion Counter -32003','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2646,'QEH 9987000-00-39614','Exchange Gain','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2647,'QEH 9987000-54-39614','PE Recharge_Allowances-Others (Supporting Non Care Related)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2648,'QEH 9987002-00-39000','KCC SWF-OUTDOOR / RECREATION GROUP','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2649,'QEH 9987007-00-39000','KCC SWF-WELLNESS PROGRAM GROUP','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2650,'QEH 1501000-00-64942','Training - Course Fees & Conference - Medicine','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2651,'QEH 1051000-00-30223','A multicenter, randomized, double-blind, placebo-controlled phase 3 trial comparing the efficacy of bevacizumab in combination with Rituximab and CHOP(RA-CHOP) versus Rituximab and CHOP(R-CHOP) in pre','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2652,'QEH 1051000-00-30243','A Phase IIIB, randomized, active controlled open-label study of Sunitinib (SUTENT?) 37.5 mg daily vs Imatinib Mesylate 800 mg daily in the treatment of patients with Gastrointestinal Stromal Tumors (G','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2653,'BTS 6176000-00-38303','OC Recharge -Other Expenses','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2654,'BTS 9987000-00-39000','BTS Staff Welfare Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2655,'BTS 9987000-00-00000','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2656,'BTS 7007000-00-38208','Bank Charge','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2657,'BH 7007000-00-38208','OC Recharges (Administrative)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2658,'HKE 5906000-00-38006','DRUGS RECHARGE ACCOUNT','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2659,'HKE 7507000-00-00000','FUNDING FROM LIONS EYE BANK OF HONG KONG','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2660,'HKE 9987000-00-00000','DEPOSIT OF SECURITY ACCESS CARD-CAR PARK','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2661,'HKE 7657000-00-65597','DONATION FOR PRC','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2665,'KH 9987000-00-35013','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2666,'KH 7007000-00-30903','Personal Data Privacy Ordinance (Personal Record)-30903','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2667,'KH 9987000-54-39614','PE Recharge_Allowances-Others (Supporting Non Care Related)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2669,'QMH 1001001-00-31580','ASOI-TEE COURSE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2670,'QMH 1051000-00-61009','Donation - Li Ka Shing Foundation Hospice Program (Capital)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2671,'QMH 1051000-00-64554','Donation - Cancer Funds','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2672,'QMH 1051000-00-64561','SKYee Fund - Installation of an Integrated Brachytherapy Unit in Clincal Oncology Department of QMH ($4M)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2673,'QMH 1153000-00-00000','ENT Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2674,'QMH 1273003-00-00000','HKU-ARP(CR)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2675,'QMH 1501000-00-64412','Donation - Intravascular Ultrasound Machine','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2676,'QMH 1501000-00-64418','Donation - Medicine (Children''s Thalassaemia Foundation)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2677,'QMH 1501000-00-64435','Jackie Chan Charitable Foundation - Provision of Glivec (STI-571) for Treatment of Leukaemia Patients','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2678,'QMH 1503000-00-00000','MED Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2679,'QMH 1951000-00-64508','Donation - Ophthalmology (Eye)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2680,'QMH 2051014-00-64527','Donation - Paediatric Cardiology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2681,'QMH 2053014-00-00000','PCG Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2682,'QMH 2251000-00-64473','Donation - Surgery','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2683,'QMH 2251000-00-64481','Donation - Vascular Disease Centre','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2684,'QMH 3103100-00-00000','SYP GOPC Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2685,'QMH 3103102-00-00000','ALC GOPC Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2686,'QMH 3113100-00-64597','Donation - FM&PHC-SYP FM Clinics','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2687,'QMH 3203200-00-64592','Donation - Accident & Emergency','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2688,'QMH 5406000-00-64665','Donation - Physiotherapy','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2689,'QMH 6026002-00-64624','Donation - Operating Theatre (CTSD)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2690,'QMH 6256000-00-31584','ASOI-WIW COURSE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2691,'QMH 6606000-00-64733','Donation - Infection Control Unit','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2692,'QMH 6756000-00-64680','Donation - CND','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2693,'QMH 7357000-00-00000','TYH Misc income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2694,'QMH 7357000-00-32085','ASOI-COMM FROM SALE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2695,'QMH 7357000-00-64711','Donation - General Fund (TYH)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2696,'QMH 9981090-00-64587','Donation - Private & Special Services','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2697,'QMH 9987000-00-00000','Reprint charge','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2698,'QEH 1501000-00-30260','A clinical Evaluation of the XIENCE V Everolimus Eluting Coronary Stent System in the Treatment of Women with de novo Coronary Artery Lesions  (Single Arm Protocol)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2699,'QEH 3203200-00-30301','  A Study on prognosis of patient receving streptokinase for STEMI','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2700,'QEH 6756000-00-30024','Post Registration Diploma Course in Intensive Care, Perioperative & Orthopedic Nursing for Nurses from Guangdong Province','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2701,'QEH 6256000-00-30047','Training programme for FCRC IIB course (14 July 2012)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2702,'QEH 9987000-00-00000','Salary Overpayment','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2704,'QEH 1051000-00-30203','A Multi-center phase II of capecitable (Xedloda) in combination with cisplatin as first line chemotherapy in patients with metastatic nasopharyngeal carcinoma-30203','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2705,'QEH 1051000-00-30209','Study of ********* in anemic patients with stage IIIB or IV non-small cell lung cancer receiving first line myelosuppressive chemotherapy (Protocol No. NH19960C)-30209','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2706,'QEH 1051000-00-30210','A randomized controlled trial of Carboplatin and Paclitaxel plus or minus Sorafenib (BAY 43-9006) in chemonaive patients with stage IIIB-V Non-small Cell Lung Cancer (NSCLC)-30210','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2707,'QEH 1051000-00-30215','Phase III study of post-operative adjuvant Lapatinib or placebo and concurrent chemoradiotherapy (SCCHN) (Study Protocol No.: EGF102988)-30215','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2708,'QEH 1051000-00-30276','Trial of Adjuvant Therapy for Patients with HER2-Positive Node-Positive or High Risk Node-Negative Breast Cancer Comparing Chemotherapy (Protocol No. BO20906)-30276','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2709,'QEH 1051000-00-30280','NPC Nude Mice Xenograft Derivation - Area of Excellence (AoE) - Center for Nasopharyngeal Carcinoma Research-30280','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2710,'QEH 1051000-00-64857','Research on late complications after cancer treatment','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2711,'QEH 1051000-00-64895','Departmental Fund - Clinical Oncology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2712,'QEH 1271000-00-30032','Midwifery Clinical Training Program for  Non-local Trained Midwives who have passed  the Midwives Council of Hong Kong Examinations-30032','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2713,'QEH 1501000-00-30236','Trial of Maraviroc. This is an open label, non-comparative, international, Phase 3b safety study of maraviroc in HIV positive, treatment-experienced patients with R5 HIV  (Protocal No. A4001050)-30236','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2714,'QEH 1501000-00-30249','PMOS-PAA-05-01 KARE-AP -30249','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2715,'QEH 1501000-00-30250','Compare Rosiglitazone versus Glipizide on the Progression of Atherosclerosis in Subjects with Type 2 Diabetes Mellitus and Cardiovascular Disease-30250','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2716,'QEH 1501000-00-30251','Atazanavir/ritonavir with Lopinavir/ritonavir, each in combination with fixed dose Tenofovir-Emtricitabine in HIV-1 infected treatment na?ve subjects (Protocol No. AI424138)-30251','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2717,'QEH 1501000-00-30265','Prolonged Release Tacrolimus (Advagraf?) in stable kidney transplant patients receiving Prograf? as maintenance immunosuppression-30265','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2718,'QEH 9987000-00-35013','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2719,'QEH 1501000-00-30291','Shock-Less Study-30291','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2720,'QEH 1501000-00-64851','Geriatric Team - Research and education in geriatric medicine ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2721,'QEH 2001000-00-64935','Training - Course Fees & Conference - O&T','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2722,'QEH 2051000-00-64814','Departmental Fund - Paediatrics','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2725,'QEH 5206000-00-30040','Development Project of Virtual Reality Software as Training Media for Brain Functions -30040','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2726,'QEH 5406000-00-32001','Hydrotherapy pool rental-32001','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2727,'QEH 6876000-00-31106','Provision of Visitor Carpark at G Zone after 6pm &  Extension of SOPC Visitor Carpark up to 24 hour-31106','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2728,'QEH 6876003-00-30825','Advertising Panel Project-30825','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2729,'QEH 7007070-00-30819','Occupational Deafness Compensation Scheme  -30819','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2730,'QEH 7357000-00-64803','Donation-In-Kind (SARS)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2731,'QEH 7607000-00-30023','China Delegates Visit-30023','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2732,'QEH 7607000-00-31104','Car Park-31104','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2733,'QEH 7657000-00-65576','PRC - Postage','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2734,'QEH 7657000-00-65797','PRC - Donation Box','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2735,'QEH 7657001-00-65584','CPRC - NPC Group (exp)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2736,'QEH 7657001-00-65585','CPRC - Breast Cancer Group','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2737,'QEH 7657001-00-65587','CPRC - Furniture & equipment','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2738,'QEH 7657001-00-65590','CPRC - Honorarium to Volunteer Clerk','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2739,'QEH 7657001-00-65594','CPRC - New Patient Group','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2740,'QEH 9987000-00-30043','Train-the Trainer Course on Simulation based Medical Education (iSIM Course)-30043','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2741,'QEH 1051000-00-30206','An open-label, randomised parallel group, multicentre, Phase III study to assess efficacy, safety and tolerability of gefitinib (IRESSA) (250mg tablet) versus carboplatin/paclitaxel doublet chemothera','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2742,'QEH 1051000-00-30222','A phase 3, multicenter, placebo-controlled, double-blind,randomized clinical trial to evaluate the efficacy of bevacizumab in combination with Tarceva TM (Erlotinib) compared with Tarceva TM alone for','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2743,'BTS 7007000-00-32009','Renting out of HA premises : Promotion Counter','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2744,'BTS 7016000-00-64911','Blood Collection Section - Publicity','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2745,'BTS 6056013-00-64913','Cord Blood Bank Services','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2746,'BTS 6176000-00-64944','Special Education Programme for Blood Transfusion Service for BTS''s 60th Anniversary','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2747,'BTS 6056000-00-38114','Sales of Blood Bags to Non-HA Hospitals','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2748,'BTS 9987000-00-00000','Deposit - Contractor','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2749,'BTS 9987000-00-00000','A/P - Temp receipt (BTS)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2750,'BTS 9987000-00-00000','Unrealized Exchange Gain','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2751,'BTS 6176023-00-38303','Deposit - Rental Venue for Blood Promotion','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2752,'BH 9987000-00-64921','Designated Donation Reserve','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2753,'BH 7357000-00-38416','OC Recharges (Electricity)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2754,'HKE 9987000-00-39000','STAFF WELFARE FUND','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2755,'KH 7007000-00-38208','Official Signature','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2756,'KH 9987000-00-39614','Bank Charges','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2757,'KH 9987000-00-39000','KH SWF','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2762,'KH 2151000-00-30287','Medical Services (Psy Dept) - 30287','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2763,'KH 7007000-00-31003','Canteen - 31003','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2764,'KH 7007000-00-32006','Renting Out of HA Premises - Rehab Shop - 32006','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2765,'KH 7007000-00-38208','Rental Deposit ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2766,'QMH 1001000-00-64611','Donation - Anaesthesiology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2767,'QMH 1051000-00-64558','Donation - Roving Hospice Unit','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2768,'QMH 1151000-00-64498','S.K.Yee Medical Foundation - ENT','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2769,'QMH 1263000-00-00000','OBS Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2770,'QMH 1501000-00-64427','S.K.Yee Medical Foundation - Medicine (Detection of Systemic Fungal and Mycobacterial Infection in BMT Recipients)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2771,'QMH 1501000-00-64442','Provision of FISH, Q-PCR laboratory tests for CML patients in QMH','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2772,'QMH 2001000-00-64543','Anita Mui Charity Foundation for O&T','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2773,'QMH 2051000-00-64522','Donation - Paediatrics (BMT)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2774,'QMH 2251000-00-64476','Donation - Surgery (Liver Transplant)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2775,'QMH 2251000-00-64484','Donation - Expansion of Francis Tien Vascular Disease Centre (5M)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2776,'QMH 3113100-00-64598','Research - Clinical Utility of Amlodipine/Atorvastatin to Improve Concomitant Cardiovascular Risk Factors of Hypertension and Dyslipidemia','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2777,'QMH 5103000-00-00000','Diet Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2778,'QMH 5206000-00-38048','Orientation program in clubhouse model','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2779,'QMH 6056000-00-64628','Donation - Pathology / Anatomical Pathology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2780,'QMH 6256000-00-64617','Donation - Radiology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2781,'QMH 7007001-00-64692','Donation - DTRC','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2782,'QMH 7357000-00-31585','ASOI-TRAINING','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2783,'QMH 7357000-00-31746','TWH/GOPCs Vending Machine Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2784,'QMH 9987000-00-00000','Photocopying charge','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2785,'QEH 9987000-00-30247','Chinese Medicine Clinic - Lab Tests, X-Rays, Ultra-sound, MRI etc','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2786,'QEH 1501000-00-30316','DEB-only SVD Worldwide Registry (Sep 11-Dec 13)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2787,'QEH 5406000-00-30046','Clinical attachment for UK Student x 2 (Jul - Aug 2012)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2788,'QEH 1051000-00-30321','Study of Erlotinib (Tarceva?) in Combination with OSI-906 or placebo in Chemonaive Patients with Advanced NSCLC (Protocol No.: OSI-906-207)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2789,'QEH 6756000-00-38211','Nursing Transcript','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2790,'QEH 7857000-00-00000','Nursing Tuition Fee','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2792,'BTS 6166000-54-38304','PE Recharge_Allowances- Others','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2793,'QEH 1051000-00-30030','Placement to Students from HKU SPACE, 4 part-time MSc students in Cancer Research Unit for completion of their final year research projects. -30030','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2794,'QEH 1051000-00-30220','Trial of AMG 706 in combination with Paclitaxel and Carboplatin for advanced non-small cell lung cancer (Protocol No. 20050201)-30220','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2795,'QEH 1051000-00-30240','An international multi-centre open-label 2-arm phase III trial of adjuvant bevacizumab in triple negative breast cancer. (Protocol No. BO20289)-30240','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2796,'QEH 1051000-00-30279','NPC Biomarker Study - Area of Excellence (AoE) - Center for Nasopharyngeal Carcinoma Research-30279','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2797,'QEH 1051000-00-30308','Protocol #: EAST - S1(01023035) Randomized controlled trail of S-1 versus docetaxel in patients with non-small cell lung cancer who have received a platinum-based treatment-30308','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2798,'QEH 1271000-00-30026','O&G Clinical Attachment to QEH-30026','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2799,'QEH 1501000-00-30232','Neurocognitive contributions to rehabilitation and medical adherence of HIV infected patients (MSS 116PM)-30232','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2800,'QEH 1501000-00-30256','HIV Resistance Project MSS 152R: Surveillance and monitoring of HIV drug resistance  (** Note: this ASOI s/h report to Government.  No PE recharged and balance must be carried forward)-30256','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2801,'QEH 1501000-00-30309','ERICOR-Asia. Long term follow up of antithrombotic management patterns in Acute Coronary Syndrome patients in Asia (Protocol #: NIS-CAP-XXX-2011/1-30309','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2802,'QEH 1851000-00-64813','Departmental Fund - Neurosurgery','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2803,'QEH 5106000-00-30027','Clinical Placement for Dietetic students of HKU-30027','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2877,'QMH 1501000-00-38051','S K Yee Medical Foundation - Second-line Chemotherapy in the Treatment of Patients with Esophageal','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2805,'QEH 5206000-00-30801','Linguistic Translation service-30801','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2806,'QEH 5456000-00-30267','P&O Fabrication Support to Deyang P&O Centre-30267','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2807,'QEH 6056000-00-30234','Clinical Trial-Tigecycline Evaluation and Surveillance Trial-30234','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2808,'QEH 6056000-00-30294','Reducing Methicillin-resistant Staphylococcus aureus (MRSA) Transmission in Residential Care Homes for the Elderly (RCHEs)-30294','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2809,'QEH 7657000-00-30817','Equipment Loan Services  -30817','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2810,'QEH 7657000-00-65566','PRC - Burn group','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2811,'QEH 7657000-00-65567','PRC - Cooley Anaeia Self-helpgp','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2812,'QEH 7657000-00-65570','PRC - Resource Library','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2813,'QEH 7657000-00-65572','PRC - Social & Therapeutic Act (subv)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2814,'QEH 7657000-00-65593','PRC - Wellcont Patient Group Activities, Exhibition & Day Camp','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2815,'QEH 7657001-00-65583','CPRC - Program Expense','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2816,'QEH 9987000-00-39614','OC Recharge -Other Expenses','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2817,'QEH 7657000-00-65580','PRC - Love Ideas, Love HK','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2818,'QEH 7657001-00-65859','CPRC - Office Equipment (The Hong Kong Cancer Fund)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2819,'QEH 6006000-00-65601','Training - Course Fees & Conference - Anaesthesia','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2820,'QEH 1051000-00-30221','A multicenter, randomized, double-blind, controlled phase 3, efficacy and safety study of Sunitinib (SU011248) in patients with advanced / metastatic non-small cell lung cancer treated with Erlotinib ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2821,'QEH 1051000-00-30242','A Phase III, randomized, double-blind, placebo-controlled multi-center study of ASA404 in combination with paclitaxel and carboplatin as first-line treatment for locally advanced or metastatic (stage ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2822,'BTS 6176000-00-38303','Deposit - Rental','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2823,'BTS 9987000-00-00000','A/P - Others','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2824,'BH 9987000-00-38202','Misc. Income (Others)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2825,'BH 7357000-00-38407','OC Recharges (Rates & Government Rent)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2826,'BH 9987000-00-00000','Recovery of loss','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2827,'HKE 9987000-00-00000','ASOI - MEDICAL SERVICE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2828,'HKE 9987000-00-30906','ASOI - PERSONAL DATA (PRIVACY) ORDINANCE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2829,'HKE 9987000-00-30814','ASOI - FILM SHOOTING','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2830,'HKE 1953000-00-38112','OTHER MISCELLANEOUS INCOME','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2831,'KH 7007000-00-38208','Other Miscellaneous Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2878,'QMH 1501000-00-38053','S K Yee Medical Foundation Grant 2006 - Use of somatostatin analogs in management of acromegaly','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2835,'QMH 1051000-00-64553','Donation - Clinical Oncology','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2836,'QMH 1051000-00-64556','Donation - Cancer Centre','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2837,'QMH 1051000-00-64559','Donation - Ms. Trixie Ting Cancer Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2838,'QMH 1101000-00-64582','Donation - Oral Maxillofacial Surgery & Dental Surgery','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2839,'QMH 1273007-00-00000','HKU-TYH-WDTC(CR)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2840,'QMH 1501000-00-64401','Donation - Medicine','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2841,'QMH 1501000-00-64410','K.K.L Diabetes Centre','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2842,'QMH 1501000-00-64416','S.K.Yee Medical Foundation - Medicine (Fludarabine)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2843,'QMH 1501000-00-64419','Donation - The Use of Fludarabine for the Non-Myeloablative Conditioning for BMT Patients','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2844,'QMH 1501000-00-64429','Provision of Drugs for Haematology & Oncology & BMT Unit','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2845,'QMH 1501000-00-64445','H K Blood Cancer Foundation - Novoseven','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2846,'QMH 2001000-00-64544','BMCPC Charity Donation 2010 - High-definition Arthroscopic System','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2847,'QMH 2051000-00-64529','Donation - Paediatrics & Adolescent Medicine - Neonatal Team (Equipment & Consumables)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2848,'QMH 2151000-00-64577','A Randomized Controlled Trial Comparing Treatment by Quetiapine (Seroquel) with and without Rebabilitation (the Clubhouse Program)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2849,'QMH 2251000-00-64482','Donation - Skin Bank','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2850,'QMH 2251000-00-64487','QMHCT - Financial Support for Robotic-assisted Surgery for Cancer Patients and Paediatric Patients','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2851,'QMH 2251000-00-64494','Research - Radiation Exposure of Operating Theatre Staff during Surgical Operation (OSH Grant 2003)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2852,'QMH 3113100-00-00000','Misc Income-Official Signature(GOPCs)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2853,'QMH 5356000-00-64640','Donation - Podiatry','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2854,'QMH 5503000-00-00000','SPTH Medical Report','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2855,'QMH 6256000-00-64618','Donation of 3T MRI System','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2856,'QMH 7297000-00-64724','Donation - Nurse Quarter','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2857,'QMH 7357000-00-31751','TYH Vending Machine Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2858,'QMH 7357000-00-64700','Interest Income','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2859,'QMH 7357000-00-64703','Transplant Training & Research Assistance Scheme (TTRAS)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2860,'QMH 7357000-00-64710','Central - People First Initiatives','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2861,'QMH 9987000-00-00000','Official signature','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2862,'QMH 9987000-00-00000','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2863,'BH 9987000-00-00000','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2864,'BH 9987000-00-35013','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2865,'HKE 7357001-00-64945','DONATION FOR HKEH 20TH ANNIVERSARY CELEBRATION','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2866,'HKE 9987000-00-00000','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2867,'HKE 9987000-00-35013','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2868,'KH 5206000-00-38004','Deposit for Hiring Hospital Appliance (OT)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2869,'KH 5406000-00-38007','Deposit for Hiring Hospital Appliance (PT)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2870,'KH 5456000-00-38005','Deposit for Hiring Hospital Appliance (P&O)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2871,'QEH 1501000-00-64947','GI Team Training Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2874,'QMH 1271007-00-38711','STATIONERY (TYH)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2875,'QMH 1501000-00-38049','S K Yee Medical Foundation Grant 2006 - Double Balloon Enteroscopy for Patients with Bowel Disease','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2876,'QMH 1501000-00-38050','Lee Hysan Founfation (HKU) - Anti-osteoporosis Treatment for Patients on CSSA','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2879,'QMH 1501000-00-38055','S K Yee Medical Foundation Grant - Provision of free alemtuzumab to patients with chronic lymphocy','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2880,'QMH 1501000-00-38056','S K Yee Medical Foundation Grant - Provision of ibritumomab for the treatment of lymphoma (HKU)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2881,'QMH 1501000-00-64411','Donation - Mong Man Wai Heart and Vessel Interventional Suite','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2882,'QMH 1501000-00-64449','Donation - Omapatrilat versus enelapril randomized trial of utility in reducing events ( OVERTURE)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2883,'QMH 2251000-00-38052','Ming Tak Cancer Fund (HKU)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2884,'QMH 6756000-00-00000','Official Transcript','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2885,'QMH 6756000-00-38044','Certificate Course in Palliative Nursing','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2886,'QMH 7007000-00-31111','Car Park fee','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2887,'QMH 7057000-00-38701','STATIONERY (QMH)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2888,'QMH 7357001-00-39011','SALE OF INTERNET BROADBAND CARD','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2889,'QMH 9987000-00-00000','WAGE-IN-LIEU OF NOTICE','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2890,'QMH 9987000-00-00000','OVERPAID SALARY','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2891,'QMH 9987000-00-39995','LOST OF STAFF ID CARD','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2892,'RC 9987000-00-00000','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2893,'RC 9987000-00-35013','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2894,'QMH 7357001-00-39011','70TH ANNIVERSARY WATCH','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2896,'RC 7007000-00-38208','OC Recharge - Courier services','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2897,'RC 7007000-00-38208','OC Recharge - paper products','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2898,'RC 7007000-00-38208','OC Recharge - Stationeries','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2899,'RC 9987000-00-30013','ASOI - Income-Training','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2900,'RC 7007000-00-38208','OC Recharge - Other office supplies','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2901,'RC 7007000-00-38208','OC Recharge - Photocopying charges','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2902,'RC 7007000-00-38208','OC Recharge - Postage charges','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2903,'RC 7007000-00-38208','OC Recharge - toner/ink cartridges','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2904,'RC 7007000-00-38208','OC Recharge - Printing charges','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2905,'RC 7007000-00-38208','OC Recharge - book refund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2906,'RC 7007000-00-38208','OC Recharge - Adm expenses','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2907,'RC 9987000-00-30908','ASOI - Income-PDPO','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2908,'KH 2151000-00-64802','Donation  - Psychiatry/Departmental Fund - 64802','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2909,'KH 2151000-00-64303','Donation - Psychiatry/Patient Benefit for Kapok Clubhouse - 64303','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2910,'KH 7357000-00-64208','Donation - HCE/KH Open Day/Ceremony/Anniversary - 64208','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2911,'KH 7357000-00-64502','Donation - HCE/Purchase of Medical Equipment - 64502','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2912,'KH 2202200-00-64505','Donation - Rehab/Purchase of medical equipment - 64505','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2913,'QEH 6606000-00-64940','Departmental Fund - Infection Control Unit','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2914,'QEH 2051000-00-64950','Love Ideas HK','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2915,'QEH 5206000-00-38004','Deposit for Hiring Hospital Appliance (OT)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2916,'QEH 5406000-00-38007','Deposit for Hiring Hospital Appliance (PT)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2917,'QEH 1501000-00-64930','Transcatheter Aortic Valve Implantation (TAVI) Program','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2918,'QEH 5456000-00-38005','Deposit for Hiring Hospital Appliance (P&O)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2919,'KH 2151000-00-64800','Donation - Psychiatry/Patient Benefit-HKBFDCP-E.health - 64800','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2920,'KH 2202200-00-64807','Donation - Rehabilitation/Departmental Fund - 64807','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2921,'KH 7657000-00-64399','Donation - PRC/Patient activity organized by Patient Resource Centre - 64399','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2922,'KH 7357000-00-64926','Donation - HCE/Environmental Protection Activities and Education Purpose - 64926','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2923,'KH 1551000-00-64801','Donation - RMD/Departmental Fund - 64801','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2924,'KH 2202200-00-64305','Donation - Rehabilitation/Patient Benefit - 64305','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2925,'KH 5406000-00-64506','Donation - Physiotherapy/Robotic Assisted Therapy System (HACF) - 64506','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2926,'QEH 1501000-00-64952','Staff Welfare and Recreational Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2927,'QEH 2251000-00-64936','Training - Course Fees & Conference - Surgery','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2928,'QEH 6916001-00-38205','Medical reports and records (Patient Service)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2929,'BTS 6176000-00-64853','Research and Development Fund for Ferritin Test for Iron Supplement Study','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2930,'KH 5406000-00-64202','Donation - Physiotherapy/Departmental Fund - 64202','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2931,'QEH 1051000-00-30323','Efficacy of Ipilimumab plus Etoposide/ Platinum in Subjects with Newly Diagnosed Extensive-Stage Disease Small Cell Lung Cancer (ED-SCLC) (Protocol No.: CA184156)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2932,'KH 7007000-00-64411','Donation - Admin/Improvement of Barrier Free Access Facilities (JCCT) - 64411','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2933,'KH 7357000-00-64306','Donation - HCE/Adm & Hospital Improvement - 64306','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2934,'KH 7357000-00-64307','Donation - HCE/Koon Wah Mirror - Hui Hoy & Chow Sin Lan Charity Fund Limited - 64307','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2935,'QEH 1501000-00-64879','Equipment & Consumables for the Catheterization Lab','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2936,'QEH 7657000-00-65560','PRC Fundraising Programme','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2937,'QEH 7657000-00-65561','PRC - Joyful Club','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2938,'KH 7357000-00-64810','Donation - HCE/Publication - 64810','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2939,'KH 7607000-00-64941','Donation - HR/Staff Wellbeing for Front Line Workers (HACF) - 64941','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2940,'KH 7657000-00-64398','Donation - PRC/Fund Raising Activity at Gala Premiere for patient service - 64398','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2941,'QEH 7017000-00-64939','QEH 50th Anniversary','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2942,'KH 7357000-00-64207','Donation - HCE/Christmas Party - 64207','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2943,'KH 9987000-00-39614','KH Donation Income - 39614','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2944,'QEH 9987000-00-39000','Staff Welfare Fund','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2945,'QEH 1501000-00-64943','Carotid Duplex USG screening for patients with acute ischaemic stroke','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2946,'QEH 1051000-00-64954','Training - Course Fees & Conference','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2947,'QEH 6756000-00-64955','Staff Wellness Program for BH Staff','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2948,'QEH 7017000-00-64956','Trophy for QEH Golden Jubilee First Celebration Activity - Walkathon','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2949,'QMH 7357000-00-38831','Credit Card Commission','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2950,'QEH 7657001-00-65600','Employing Assistant Social Work Officer','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2951,'QEH 1501000-00-30324','Trail of pregabalin controlled release formulation as adjunctive therapy in adults with partial onset seizures (Study protocol: A0081194)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2952,'QEH 1051000-00-30048','“Gene-environment interaction in the causation of nasopharyngeal carcinoma ?A case–control study among Hong Kong Chinese','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2953,'QEH 6876003-00-31106','Provision of Visitor Carpark at G Zone after 6pm &  Extension of SOPC Visitor Carpark up to 24 hour-31106','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2954,'QEH 6056000-00-64946','FISH test and immunostaining for detection of ALK gene translocation in NSCLC (30 cases)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2955,'QEH 1051000-00-30325','First Line Treatment of Patients with HER2-positive Advanced (Metastatic of Locally Repcurrent) Breast Cancer (Protocol no.: MO28047)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2956,'QEH 5206000-00-30045','Providing ??? training for cancer rehabilitation patient in CANCER LINK (Central)-30045','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2957,'QEH 6916000-00-38207','Medical reports and records (Medical Record)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2958,'QMH 9987000-00-00000','Exchange Diff','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2966,'KH 7657000-00-64409','Donation - PRC/Toward Barrier Free Society - 64409','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2967,'BTS 6936000-00-39603','Laboratory Supplies (BMDR)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2968,'QEH 6056000-00-64957','Her-2 Tests for gastric cancer patient','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2969,'QMH 7007002-00-38006','Program with Central and Western District Council','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2970,'BTS 6936000-00-39603','OC Recharge -Other Expenses (BMDR)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2971,'BTS 6936000-54-39603','PE Recharge_Allowances- Others (BMDR)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2972,'QEH 3103100-00-39607','HHC Medical reports and records ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2973,'QEH 6056000-00-30051','Clinical Training on electronic cross-matching and OTBTS (18 June 2013 - 11 July 2013)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2974,'QEH 3103100-00-39609','LKKC Medical reports and records ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2975,'KH 1551000-00-30328','Medical Services (RMD Dept) – 30328','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2976,'QEH 3103100-00-39608','LKMD Medical reports and records ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2977,'QEH 7857000-00-30050','Tuition Fee of Higher Diploma in Nursing Programme (2013-2014)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2978,'QEH 3103100-00-39606','CKHC Medical reports and records ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2979,'QEH 3103100-00-39610','YMTJCC Medical reports and records ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2980,'QEH 1271000-00-30049','Chinese Congress of Obstetrical Research  (22 Mar 2013 - 23 Mar 2013)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2981,'QEH 1051000-00-30329','Protocol # VEG116261 STAR: A population-based natural history study of patient and tumor characteristics, treatment patterns and disease progression','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2982,'KH 7707000-00-38209','Contractor Deposit','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2983,'NLT 7007000-00-38943','OFFICE SUPPLIES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2984,'NLT 1271000-00-38073','ADMINISTRATIVE EXP - LINE RENTAL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2985,'NLT 7007000-00-00000','LINE RENTAL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2986,'NLT 4105000-00-00000','COMMUNICATION - LINE RENTAL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2987,'NLT 7007000-00-38948','COMMUNICATION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2988,'NLT 7007000-00-38950','MAINTENANCE MATERIALS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2989,'NLT 7807000-00-39055','R&M - OTHER EQUIPMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2990,'NLT 6006000-00-38751','REFUND OF EXAMINATION FEE - ANAESTHETICS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2991,'NLT 2051000-00-38191','TRAINING-COURSE FEE - GEN. PAEDI','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2992,'NLT 6056000-00-38771','TRAINING - COURSE FEE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2993,'NLT 6006000-00-38751','TRAINING - PUBLICATIONS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2994,'NLT 7007000-00-38941','OTHER TRAINING-UNALLOCABLE (GEN. ADMIN.)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2995,'CMC 7007000-00-31801','ASOI - Others (Medical Report Photocopying charges)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2996,'CMC 7007000-00-31806','ASOI - Others (Air conditioning charges - Intern)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2997,'CMC 9987000-00-38750','Medical Drugs','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2998,'CMC 9987000-00-39143','Bank Charges','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2999,'CMC 9987000-00-00000','Medical Reports & Records','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3000,'CMC 9987000-00-00000','Interest Income','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3001,'CMC 9987000-00-00000','Rental - Market Rent Quarters','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3002,'CMC 3113112-00-00000','Miscellaneous income - GOPC-NS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3003,'NLT 9987000-54-00000','BASIC SALARY - CONTRACT STAFF','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3004,'NLT 2051000-54-00000','OTHER TEMPORARY STAFF','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3005,'NLT 1051000-04-00000','HONARARIA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3006,'NLT 2001000-04-00000','HONORARIA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3007,'NLT 9987000-04-00000','CONTRACT STAFF W /GRATUITY/ MPF CONTR','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3008,'NLT 9987000-04-00000','HLISS CONTRIBUTION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3009,'NLT 6026000-00-38763','MEDICAL CONSUMABLES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3010,'CMC 2251000-00-64082','Donation - Surgical - The late of Dr P C Cheng','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3011,'CMC 3113100-00-64202','Donation - WK GOPC','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3012,'CMC 3113132-00-64203','Donation - S K Yee-CSWC','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3013,'CMC 7007037-00-64224','Donation - CMC Redevelopment Project Phase II','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3014,'CMC 1501000-00-31544','ASOI - Training - M&G - OUHK - Clinical Placement - EN Conversion','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3015,'CMC 1501000-00-31545','ASOI - Training - M&G - OUHK - Clinical Placement - BN','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3016,'CMC 2251000-00-31548','ASOI - Training - Surgery - Poly U','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3017,'CMC 2251000-00-31549','ASOI - Training - Surgery - OUHK - Clinical Placement - EN Conversion','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3018,'CMC 2251000-00-31551','ASOI - Training - Surgery - OUHK - HDNS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3019,'CMC 3203200-00-31562','ASOI - Training - A&E - OUHK - Clinical Placement - BN','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3020,'PMH 4105000-00-38601','TRAINING - COURSE FEE CNS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3021,'PMH 7857000-00-00000','TRAINING-PUBLICATIONS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3022,'PMH 6056052-00-38793','QUALITY CONTROL PROGRAM','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3023,'PMH 7007000-00-38943','LICENCES & REGISTRATION - DANGEROUS GOOD','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3024,'PMH 3113121-00-38443','GOPC - BLDG CONSTRUCTION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3025,'PMH 7357000-00-00000','ADMIN. EXP. - BUILDING CONSTRUCTION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3026,'PMH 1851000-00-38163','ADMIN. EXP. - PROFESSIONAL FEE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3027,'PMH 2202201-00-38283','ADMIN. EXPENSES - F&E OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3028,'PMH 6056000-85-00000','PRIVATE OUT-PATIENT ANATOMICAL PATHOLOGY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3029,'PMH 7507000-79-00000','OFFICIAL SIGNATURE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3030,'PMH 9987000-00-00000','ATM','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3031,'PMH 9987000-00-00000','OVERPAYMENT - PATIENT (CASH)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3032,'PMH 5206001-00-00000','PPMI - OCCUPATIONAL THERAPY (LKB)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3033,'PMH 5453000-00-00000','PPMI - PROSTHETICS & ORTHOTICS (SOPC)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3034,'PMH 5456000-00-00000','PPMI - PROSTHETICS & ORTHOTICS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3035,'PMH 5906000-00-00000','PPMI - PHARMACY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3036,'PMH 9987000-00-00000','CREDIT CARD REBATE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3037,'PMH 9987000-00-39825','STAFF WELFARE FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3038,'PMH 5206000-00-00000','PATIENT VOCATIONAL & RETRAINING BOARD','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3039,'PMH 1501000-00-31603','ASOI - CLINICAL PROJECT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3040,'NLT 7007000-00-38943','PART TIME INTERPRETATION SERVICE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3041,'NLT 7007000-00-38943','LICENCES & REGISTRATION - DANGEROUS GOOD','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3042,'NLT 2202200-00-00000','ADMIN EXP - FURNITURE (<$100,000)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3043,'NLT 7507000-79-00000','OFFICIAL SIGNATURE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3044,'NLT 7607000-83-00000','ASOI - CODE OF ACCESS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3045,'NLT 1051000-00-31607','ASOI - INCOME','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3046,'NLT 5106000-00-31599','ASOI INCOME - TRAINING','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3047,'NLT 9987000-00-31401','ASOI-CO-OP/OUTWARD SCHEME','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3048,'NLT 7357000-00-39055','BANK CHARGES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3049,'NLT 7807000-00-39163','ADMIN EXP - NON-CAPITALISED- F&E','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3050,'NLT 6756000-00-39055','HOSPITAL SUPPLIES - OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3162,'KCH 5206000-00-31402','ASOI Circle T - 31402','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3163,'KCH 2151017-00-31508','ASOI Social Thinking Training - 31508','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3164,'KCH 2151016-00-31509','ASOI Clinical Attachment (JCCPA) - 31509','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3165,'KCH 2151016-00-31601','ASOI EHCCS - 31601','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3166,'KCH 2152015-00-31607','ASOI Commissioned Research on Mental Health (SMH-39) - 31607','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3167,'KCH 2153006-00-31703','ASOI Vending Machine - YMTCPC - 31703','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3168,'KCH 5206000-00-31803','ASOI SES (SWD - 10 places) - 31803','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3169,'KCH 7007000-00-38948','Hospital supplies (others)- Admin','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3170,'KCH 9987000-00-39210','Rates (staff canteen)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3171,'KCH 7007000-00-38962','JOS items','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3172,'KCH 7007000-00-38968','Electricity','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3173,'KCH 9987001-00-31101','ASOI - Deposit of smart cards carpark','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3174,'KCH 9987000-00-00000','Deposit for Access cards','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3175,'KCH 7007000-00-39050','Repair charges for Hospital vehicle','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3176,'KCH 7007000-00-38949','Line rental','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3177,'NLT 7357000-00-00000','OTHER SUPPLIES- M COMPUTER-CONSUMABLES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3178,'NLT 7027000-00-31803','ASOI - OTHERS (NEWSPAPER)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3179,'NLT 7007000-00-38943','OFFICE SUPPLIES - POSTAGE CHARGES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3180,'NLT 7357000-00-00000','OTHER SUPPLIES-OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3181,'NLT 7007000-00-38948','UTILITIES-GAS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3182,'NLT 7007000-00-38943','COMMUNICATIONS - OFFICE TEL. RENTAL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3183,'NLT 7007000-00-38948','COMMUNICATION-NON-STANDARD TEL. CHARGES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3184,'NLT 7007000-00-38943','REPAIR & MAINTENANCE-ALTERATION(GEN ADM)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3185,'NLT 6256000-00-39231','TRAINING - COURSE FEE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3186,'NLT 6056000-00-38771','TRAINING-SUBSISTENCE ALLOWANCE PATHOLOGY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3187,'CMC 7007000-00-31519','ASOI - Training - Admin','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3188,'CMC 7027000-00-31803','ASOI - Others (Library)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3189,'CMC 6256000-00-31804','ASOI - Others (Income from wet silver flakes)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3190,'CMC 7007000-00-31808','ASOI - Others (Advertising revenue)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3191,'CMC 7007000-00-31901','ASOI - Personal Data','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3192,'CMC 6256000-00-31902','ASOI - Personal Data - X-ray film dulplication charges','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3193,'CMC 5406000-00-32002','ASOI - Income from Rental of Premises (Hydro Pool)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3194,'CMC 7007000-00-38942','Rates & Government Rent','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3195,'CMC 7007000-00-38943','Utility Charges','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3196,'CMC 9987000-00-00000','Utility Charges - Market Rent Quarters','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3197,'CMC 9987000-00-00000','Miscellaneous income','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3198,'CMC 9987000-00-00000','Deposit - Contractor','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3199,'NLT 9987000-00-35013','C/A - HA Charitable Foundation','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3200,'NLT 1501000-14-00000','BASIC SALARIES - DEPT OF MED & GERI','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3201,'NLT 2001000-14-00000','BASIC SALARIES - DEPT OF O&T','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3202,'NLT 9987000-04-00000','HONORARIA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3203,'NLT 9987000-04-00000','CASH ALLOWANCE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3204,'NLT 6756000-54-00000','CASH ALLOWANCE FOR TRAINING NURSE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3205,'NLT 6056000-00-38773','LABORATORY SUPPLIES - PATHOLOGY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3206,'NLT 2202201-00-38284','MEDICAL CONSUMABLE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3207,'CMC 1352000-00-64059','Donation - Hospice','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3208,'CMC 2001000-00-64091','Donation - Orthopaedic','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3209,'CMC 6756000-00-64151','Donation - CND - Nurses Training','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3210,'CMC 5906000-00-64181','Donation - Pharmacy','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3211,'CMC 5406000-00-64182','Donation - Physiotherapy','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3212,'CMC 5106000-00-64192','Donation - Dietetic Dept','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3213,'CMC 7657000-00-64229','Donation - PRC - Benefit to patient','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3214,'CMC 9987000-00-64231','Donation - Hospital Donation Interest','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3215,'CMC 9987000-00-64233','Donation -','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3216,'CMC 1261000-00-64403','Donation - Don Box - Antenatal Training Course','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3217,'CMC 6756000-00-31533','ASOI - Training - CND - OUHK - HDNS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3218,'CMC 4105000-00-31503','ASOI - Training - CNS - Poly U','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3219,'CMC 2051000-00-31553','ASOI - Training - Paed - OUHK - Clinical Placement - EN Conversion','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3220,'CMC 2051000-00-31554','ASOI - Training - Paed - OUHK - Clinical Placement - BN','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3221,'CMC 3203200-00-31560','ASOI - Training - A&E - Poly U','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3222,'CMC 3003000-00-31510','ASOI - Training - OPD','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3223,'CMC 6026000-00-31568','ASOI - Training - Operating Theatre - OUHK - HDNS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3224,'PMH 2051000-00-38191','TRAINING-COURSE FEE - GEN. PAEDI','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3225,'PMH 3113125-00-38483','GOPC - BLDG CONSTRUCTION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3226,'PMH 7007000-00-38950','PROFESSIONAL FEE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3227,'PMH 7007000-00-38946','ADMIN EXP - NON-CAP -IMPROVEMENT (OTHER)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3228,'PMH 5206000-00-38673','ADMIN EXP - NON CAP - F&E (<$100,000)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3229,'PMH 7357000-00-39055','ADMIN. EXP. - FURNITURE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3230,'PMH 2202200-00-00000','ADMIN EXP - FURNITURE (<$100,000)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3231,'PMH 9987000-00-52060','ADMINISTRATIVE EXPENSES - OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3232,'PMH 6086000-79-00000','HAEMATOLOGY AND SEROLOGY UA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3233,'PMH 7007000-00-00000','OTHER SUNDRY DEPOSITS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3234,'PMH 9987000-00-00000','PREPAYMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3235,'PMH 6016044-00-66016','CLINICAL DEPARTMENT - PPI (OTHERS)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3236,'PMH 9987000-00-00000','DESIGNATED DONATION RESERVE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3237,'PMH 9987000-00-00000','DONATIONS INCOME -GENERAL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3238,'PMH 2251000-00-31508','ASOI - GENERAL SURGERY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3239,'PMH 3003000-00-32002','ASOI-RI','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3240,'PMH 3113128-00-00000','ASOI - GOPC (TOC) - DATA ACCESS REQUEST','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3241,'PMH 4105000-00-31804','ASOI - OTHERS (CNS)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3242,'PMH 5206000-00-31511','ASOI - TRAINING (OCC. THERAPY)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3243,'PMH 5406000-00-31699','ASOI - MED. SERVICES (PHYSIO)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3244,'NLT 1501000-00-38113','POSTAGE/COURIER SERVICES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3245,'NLT 7357000-00-00000','FILIM PROCESSING','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3246,'NLT 7357000-00-00000','ADMIN. EXP. - BUILDING CONSTRUCTION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3247,'NLT 7007000-00-38946','ADMIN EXP - NON-CAP -IMPROVEMENT (OTHER)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3248,'NLT 7357000-00-39055','NON-CAPITALISED - F & E - OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3249,'NLT 3113120-00-00000','GOPC-HKCC-OFFICIAL SIGNATURE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3250,'NLT 2051000-00-31510','PAEDIATRIC DIALYSIS COURSE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3251,'NLT 5206000-00-00000','ASOI-COMM.REHAB.PRACTITIONER PROGRAMME','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3252,'NLT 6056000-00-31601','ASOI-MED.SERVICES-PATH.TESTS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3253,'NLT 7007000-00-32003','ASOI-PREMISES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3254,'NLT 7857000-00-00000','ASOI - OTHERS-NUR.SCHOOL -PC&F/LT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3255,'NLT 9987000-00-31604','ASOI INCOME','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3256,'NLT 9987000-00-31902','ASOI-PDPO','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3257,'NLT 9987000-00-00000','EXCHANGE GAIN','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3258,'NLT 7357000-00-00000','HOSPITAL SUPPLIES - OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3259,'NLT 6116000-00-00000','REAGENTS READY FOR USE - BACTERIOLOGY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3260,'NLT 5906000-00-00000','MEDICAL DRUGS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3261,'NLT 1501000-92-39811','MEDICAL EQUIPMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3262,'NLT 2051000-00-38193','GENERAL MEDICAL EQUIPMENT <$100,000','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3263,'NLT 3113126-00-00000','GOPC-MWC-MEDICAL REPORT/LAB. RESULT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3264,'NLT 3113127-00-00000','GOPC-TCHC-PHOTOCOPYING','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3265,'NLT 6606000-00-00000','MISC.INCOME-OUTSIDE WORK','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3266,'NLT 9987000-00-39143','RECHARGE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3267,'NLT 7007000-00-38948','PROPERTY CHARGES AND UTILITIES - WATER','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3268,'NLT 7357000-00-39055','OTHER MEDICAL SUPPLIES-EXEC SUP TO HCE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3269,'NLT 3003000-79-00000','CIVIL SERV. DENTURES DENTAL APPL-SALES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3270,'NLT 5453000-71-00000','EP PROST & ORTHO APPL - SALES UA SKCPC','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3271,'NLT 5456000-75-00000','P & O -SALE/HIRE CHARGES(OP-NEP)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3272,'NLT 5456000-85-00000','PRO & ORTH PRIVATE OUTPATIENT CHA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3273,'NLT 9987000-79-00000','OTHER SALES UA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3274,'PMH 2001000-14-00000','BASIC SALARIES - DEPT OF O&T','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3275,'PMH 4105000-14-00000','BASIC SALARIES - CNS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2959,'HO 9987000-00-35013','C/A - HA Charitable Foundation','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2960,'QEH 9987000-00-00000','SFI for Standalone Invoice ','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2961,'QMH 1501000-00-38063','S.K.Yee Medical Foundation Grant 2010-Providing intravenous paricalcitol treatment to the sick and poor chronic hemodialysis patients (HKU)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2962,'QMH 6756000-00-38081','NURSE QUARTER MESS','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2963,'QMH 1501000-00-38049','S K YEE FOUNDATION GRANT 2006','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2964,'QEH 1051000-00-30327','Subcutaneous administration of Rituximab versus intravenous Rituximab (Protocol no.: MO28457)','T','F','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (2965,'ZT','Irrecoverable debt to be recovered from TWG','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3051,'NLT 6076000-00-00000','REAGENTS READY FOR USE-STAFF HEALTH PROG','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3052,'NLT 7007000-00-38947','ELECTRICITY CHARGES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3053,'NLT 7357000-00-39055','INCIDENTAL EXPENSES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3054,'NLT 2001000-04-00000','OTHER ALLOWANCES-OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3055,'NLT 9987000-00-00000','RENTAL FEE - HYDROTHERAPY POOL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3056,'NLT 9987000-00-00000','OVERPAYMENT OF SALARY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3057,'NLT 5203000-85-00000','PRI. OUT-PAT. PRESSURE GARMENTS SPLINTS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3058,'NLT 5406000-79-00000','SALES - STICK/WOODEN CRUTCHES UA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3059,'PMH 1501000-14-00000','BASIC SALARIES - DEPT OF MED & GERI','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3060,'PMH 7807000-24-00000','BASIC SALARY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3061,'PMH 2051000-04-00000','OTHER TEMPORARY STAFF','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3062,'PMH 1051000-04-00000','HONARARIA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3063,'PMH 9987000-14-00000','CASH ALLOWANCE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3064,'PMH 9987000-00-31101','ASOI - LKB CARPARK','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3065,'PMH 9987001-00-00000','ASOI - LKB - CO-OP SHOP/OUTWARD SCHEME','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3066,'PMH 7007000-00-00000','KIOSK (7-11) - DEPOSIT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3067,'PMH 3113122-00-00000','GOPC-SKCC--MEDICAL REPORT/LAB.RESULT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3068,'PMH 1051000-04-00000','HLISS CONTRIBUTION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3069,'PMH 9987000-04-00000','HLISS CONTRIBUTION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3070,'PMH 7357000-00-00000','OTHER SUPPLIES-OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3071,'PMH 3003000-00-00000','COMMUNICATION-LINE RENTAL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3072,'PMH 7007000-00-38948','TELEPHONE RENTAL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3073,'PMH 4105000-00-31503','COMMUNICATION - OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3074,'PMH 7007000-00-38948','COMMUNICATION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3075,'PMH 7007000-00-38950','MAINTENANCE CONTRACT - BUILDINGS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3076,'PMH 3113100-00-00000','R&M - MAIN.CONTRACT - BUILDINGS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3077,'PMH 6006000-00-38751','REFUND OF EXAMINATION FEE - ANAESTHETICS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3078,'GH 7357000-00-32085','ASOI - PROMOTION COUNTER','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3079,'GH 7257000-00-38721','WATER & SEWAGE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3080,'GH 1601001-00-64288','S K YEE MEDICAL FOUNDATION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3081,'ML 9987000-00-35013','Donation To HA Charitable Foundation','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3082,'ML 1503000-00-00000','MED Medical Report','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3083,'ML 5203000-00-00000','OCC Medical Report','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3084,'TWH 9987000-00-00000','TENDER DEPOSIT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3085,'TWH 1151000-00-31594','ASOI - ENT TRAINING COURSE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3086,'TWH 5106000-00-31891','ASOI-OTHERS (HEALTHY DIET WORKSHOP)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3087,'TWH 7357000-00-31741','ASOI - VENDING MACHINE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3088,'TWH 9987000-00-35013','DONATION TO HA CHARITABLE FOUNDATION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3089,'TWH 2251000-00-64819','SURGERY FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3090,'TWH 3103100-00-64813','DONATION FUND FOR OUTPATIENT DEPARTMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3091,'TWH 7007000-00-64801','EDUCATION & INSTRUMENT FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3092,'TWH 7657000-00-64816','PATIENT RESOURCE CENTRE FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3093,'TWH 5156000-00-38027','PATIENT SUPPORT GROUP','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3094,'TWH 7357000-00-38001','STAFF WELFARE FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3095,'TWH 5406000-00-64823','PHY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3096,'PMH 3113124-00-00000','GOPC-TYCH-PHOTOCOPYING','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3097,'PMH 3113128-00-00000','GOPC-TOC-PHOTOCOPYING','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3098,'PMH 7007000-79-00000','RECOVERY OF ELECTRICITY CHARGES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3099,'PMH 9987001-00-00000','LKB - MISC. INCOME','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3100,'PMH 7007000-00-38947','ELECTRICITY CHARGES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3101,'PMH 7357000-00-39055','ELECTRICITY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3102,'PMH 1051000-00-00000','ELECTRICITY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3103,'PMH 2202201-00-39055','RATES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3104,'PMH 5203000-79-00000','OCCU/PHYSTIO THERAPY APPL-SALES UA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3105,'PMH 5403000-85-00000','SKCP - METAL CRUTCHES / W. FRAME - SUPPL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3106,'PMH 5456000-79-00000','LKB-P&O-SALE/HIRE CHARGE (IP-GS)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3107,'PMH 7707000-79-00000','SALE OF HA BYLAWS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3108,'KWH 9987000-00-38954','Utilities - Others','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3109,'KWH 9987000-00-38949','OC Recharge (Pest Control)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3110,'KWH 9987000-00-39147','Medical Equipment','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3111,'KWH 9987000-00-38953','Computer Hardware','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3112,'KWH 5003000-00-00000','Sales of Ear Mould','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3113,'KWH 9987000-00-31401','ASOI - Co-op Shop','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3114,'KWH 9987000-00-32003','ASOI - Sexual Violence Crisis Centre','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3115,'KWH 9987000-00-00000','Rental of Hydro Pool','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3116,'KWH 9987000-00-00000','Deposit - Rental Paid','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3117,'KWH 9987000-00-64220','Donation to O&G (MS)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3118,'KWH 9987000-00-64290','Donation to Physio (MS)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3119,'KWH 9987000-00-64410','Donation to Geri Day (MS)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3120,'KWH 9987000-00-64904','Donation to Pain Clinic & Pain Support Group','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3121,'WTS 9987000-00-39165','Computer Hardware (Others)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3122,'WTS 9987000-00-31901','ASOI - Personal Data (Privacy) Ordinance','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3123,'WTS 9987000-00-00000','Overpayment of Salary','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3124,'WTS 9987000-00-00000','Deposit - Others','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3125,'DKC 6026000-00-38721','EMSD-Operating Theatre','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3126,'DKC 9987000-00-00000','Exchange Gain','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3127,'DKC 7357000-00-31911','ASOI - Personal Data (Privacy) Ordinance','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3128,'DKC 9987000-00-00000','Rental Deposit - Kiosk','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3129,'DKC 9987000-00-38021','Deposit Keys','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3130,'DKC 1003000-00-00000','ANA Medical Report','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3131,'DKC 9987000-00-00000','Auction Sale','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3132,'FYK 7007000-00-38801','OTHER OFFICE SUPPLIES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3133,'FYK 7007000-00-38801','RECHARGE OF COMMUNICATION CHARGES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3134,'FYK 7357000-00-38831','INCIDENTAL EXPENSES - OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3135,'FYK 7357000-00-31741','ASOI - VENDING MACHINE / COIN TELEPHONE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3136,'FYK 9987000-00-00000','DONATION TO HA CHARITABLE FOUNDATION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3137,'GH 7007000-00-31741','ASOI-VENDING MACHINE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3138,'KCH 9987000-00-00000','Transcript of training','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3139,'PWH 7607061-00-39203','Staff Welfare Fund - Outing','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3140,'PWH 6916000-00-31901','ASOI - PERSONAL DATA (PRIVACY) ORDINANCE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3141,'PWH 7027000-00-31802','ASOI - INTER LIBRARY LOAN INCOME','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3142,'PWH 7007000-00-32007','ASOI - DEPOSIT - KIOSKS SERVICE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3143,'PWH 7297001-00-00000','QUARTER RATES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3144,'PWH 1151004-00-64004','Donation to Department of ENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3145,'PWH 1401004-00-64003','Donation to Department of A&ICU','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3146,'PWH 1851004-00-64003','Donation to Private Ward','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3147,'PWH 2251004-00-64003','Donation to Department of Surgery','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3148,'PWH 2251004-00-64004','Donation to Department of Surgery - Staff T&D','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3149,'YCH 9987000-00-00000','Deposit-others_01 fund','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3150,'YCH 5406000-00-00000','Walking aids deposit - physiotherapy','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3151,'YCH 2001000-00-00000','Walking aids deposit - Orth','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3152,'YCH 9987000-00-00000','Communication - others','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3153,'YCH 9987000-00-00000','Deposit-photocopy charges -hospital library','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3154,'YCH 9987000-31-11046','Cash Allowances','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3155,'YCH 3103129-00-00000','Temp Receipts_01 fund_Lady Trench','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3156,'OLM 3103118-00-38411','SUNDRY - OFFICIAL SIGNATURE & RECORD - WTH GOPC','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3157,'OLM 9987000-00-00000','SUNDRY - RECOVERY OF LOSS OF STAFF CARD','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3158,'OLM 9987000-00-00000','SUNDRY - SALARY OVERPAYMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3159,'OLM 9987000-00-00000','SUNDRY - STAFF WELFARE FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3160,'OLM 4105000-00-31501','ASOI - CLINICAL PLACEMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3161,'OLM 6756000-00-31503','ASOI - NURSES TRAINING','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3985,'PWH 7297001-00-00000','DEPOSIT - FURNISHED QUARTERS RENTAL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3986,'PWH 9987000-00-00000','OTHER INCOME - RENTAL OF HYDROTHERAPY POOL TO PGS/NGOS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3987,'PWH 1051001-00-64001','PWHCF Donation to Clinical Oncology','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3988,'PWH 1271004-00-64004','Donation to Department of O&G','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3989,'PWH 3553404-00-64004','Donation to Department of Family Medicine - Staff T&D','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3990,'PWH 5106005-00-64004','Royalty for Renal Nutrition Book (Dietetics)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3991,'PWH 9987000-00-00000','Other Income','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3992,'YCH 9987000-00-00000','Designated donation','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3993,'YCH 9987000-00-00000','Wages in lieu of notice','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3994,'YCH 7297000-00-00000','key deposit','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3995,'YCH 9987000-00-00000','C/A - Hospital Governing Bodies','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3996,'YCH 9987000-00-00000','A/P others_53 fund','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3997,'YCH 9987000-00-00000','Non designated donation','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3998,'YCH 9987000-00-00000','Proceeds from disposal of Fixed Assets','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3999,'YCH 9987000-31-11046','MPF Contr - Contract Staff W/Gratuity','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4000,'YCH 3103130-00-00000','Official Signature_Mrs Wu York Yu','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4001,'OLM 9987000-00-00000','SUNDRY - TEMPORARY RECEIPT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4002,'OLM 1501000-00-64065','DONATION - MEDICAL N4','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4003,'OLM 2251000-00-64082','DONATION BOX - SURGICAL WARD','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4004,'OLM 3003000-00-64157','DONATION - OPD','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4005,'OLM 9987000-00-64071','DONATION RECEIVED FROM THE MMWA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4006,'OLM 1623000-00-31617','ASOI - DOUBLE BLIND STUDY-BI-1245.28','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4007,'OLM 1623000-00-31618','ASOI - ASOI - TAKEDA-SYR-322-402','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4008,'KCH 5203003-00-31405','ASOI WKPC Kiosk - 31405','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4009,'KCH 2152000-00-31505','ASOI Clinical Teaching (HKU) - 31505','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4010,'KCH 2151016-00-31506','ASOI Elderly in Mood Problems Workshop - 31506','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4011,'KCH 6756000-00-31507','ASOI Managing Difficult & Potentially Violent Customers - 31507','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4012,'KCH 2151016-00-31515','ASOI Domestic Helpers Training Workshop - 31515','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4013,'KCH 2152000-00-31604','ASOI Clinical Drug Trial - FIJ-MC-B019 - 31604','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4014,'KCH 2152000-00-31606','ASOI Clinical Trial - Paliperidone palmitatc - 31606','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4015,'KCH 2152000-00-31609','ASOI Sustain study - 31609','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4016,'KCH 7007000-00-31805','ASOI Rental of Nursing Quarter - 31805','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4017,'KCH 9987002-00-31903','ASOI PDPO - EKPC - 31903','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4018,'KCH 7657000-00-00000','PRSC - DON','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4019,'KCH 5406000-00-00000','Physio -DON','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4020,'KCH 7357000-14-00000','PE recharge','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4021,'KCH 6756000-14-00000','PE recharge - CND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4022,'KCH 7007000-00-38948','Office supplies (postage) - Admin','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4023,'KCH 7007000-00-38968','Rate','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4024,'KCH 9987000-00-00000','Advertising income','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4025,'KCH 7007000-00-39050','Repair charges for drop bar','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4026,'NLT 7357000-00-00000','OFFICE SUPPLIES - BOOKS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4027,'NLT 7357000-00-00000','POSTAGE CHARGES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4028,'NLT 4105000-00-00000','PROPERTY CHARGES - RENTS & RATES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4029,'NLT 7357000-00-39055','VEHICLE EXPENSES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4030,'CMC 1953000-00-31570','ASOI - Training - Ophthalmology - OUHK - Clinical Placement - BN','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4031,'CMC 7007000-00-31701','ASOI - Vending Machine','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4032,'CMC 9987000-00-38783','Laboratory Supplies','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4033,'CMC 7007000-00-38943','Water & Sewage','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4034,'CMC 9987000-00-35013','C/A - HA Charitable Foundation','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4035,'NLT 1271000-14-00000','BASIC SALARIES - DEPT OF O&G','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4036,'NLT 9987000-04-00000','BASIC SALARY - CONTRACT STAFF','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4037,'NLT 6866000-54-00000','TEMPORARY STAFF -EXTERNS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4038,'NLT 9987000-54-00000','OTHRE TEMPORARY STAFF','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4039,'NLT 9987000-24-00000','CASH ALLOWANCES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4040,'NLT 5206000-00-31801','LONG SERVICE / SERVERANCE PAYMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4041,'NLT 2001000-00-39214','DRESSINGS-OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4042,'NLT 5206000-00-38673','OTHER MED. SUPPLIES-OCCUPATIONAL THERAPY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4043,'NLT 7357000-00-00000','HOSPITAL SUPPLIES -FOOD PROVISIONS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4044,'CMC 1541000-00-64057','Donation - Geriatric','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4045,'CMC 1352031-00-64060','Donation - Don Box - Hospice','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4046,'CMC 1501033-00-64070','Donation - M&G - Ho Tung Fund - Electric Beds','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4047,'CMC 2251000-00-64081','Donation - Surgical','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4048,'CMC 1752000-00-64101','Donation - DDU','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4049,'CMC 1752000-00-64102','Donation - DDU - Macquarie Foundation Fund','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4050,'CMC 2051000-00-64116','Donation - Paediatric','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4051,'CMC 2051037-00-64117','Donation - Paediatric - Project Sun Net','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4052,'CMC 1953000-00-64121','Donation - Eye','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4053,'CMC 7027000-00-64171','Donation - Library Books','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4054,'CMC 5206034-00-64187','Donation - BMCPC-Occup-Geriatric Chair','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4055,'CMC 5206032-00-64189','Donation - SKYEE-Occup-Mobile Rehab','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4056,'CMC 3113111-00-64204','Donation - CSW - JC GOPC','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4057,'CMC 1271000-00-64401','Donation - O&G','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4058,'CMC 6056000-00-64421','Donation - Pathology','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4059,'CMC 7007000-00-64501','Donation - HACF donation - Staff Welfare','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4060,'CMC 7007000-00-31001','ASOI - Canteen','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4061,'CMC 9987000-00-31101','ASOI - Car Park','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4062,'CMC 7007000-00-31103','ASOI - Vehicle Impounding','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4063,'CMC 5056000-00-31501','ASOI - Training - Clinical Psychology','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4064,'CMC 6756000-00-31530','ASOI - Training - CND - Poly U','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4065,'CMC 6756000-00-31531','ASOI - Training - CND - OUHK - Clinical Placement - EN Conversion','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4066,'CMC 1501000-00-31504','ASOI - Training - M&G - HKU','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4067,'CMC 2001000-00-31558','ASOI - Training - O&T - OUHK - Clinical Placement - BN','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4068,'CMC 3203200-00-31509','ASOI - Training - A&E - HKU','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4069,'CMC 6026000-00-31566','ASOI - Training - Operating Theatre - OUHK - Clinical Placement - EN Conversion','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4070,'PMH 3203200-00-38541','OTHER TRAINING - UNALLOCABLE (A & E)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4071,'PMH 1541000-00-00000','OTHER TRAINING-UNALLOCABLE (GERIATRICS)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4072,'PMH 7007000-00-00000','LICENCES & REGISTRATION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4073,'PMH 3113100-00-00000','BUILDING CONSTRUCTION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4074,'PMH 3113100-00-00000','BUILDING SERVICES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4075,'PMH 7007000-00-38943','ADMINISTRATION EXP-NON CAP. F&E OTHER','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4076,'PMH 9987000-00-12011','ADMINISTRATIVE EXPENSES - OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4077,'PMH 3113121-00-00000','GOPC-NKCC-OFFICIAL SIGNATURE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4078,'PMH 6076000-85-00000','PRIVATE OUT-PATIENT CHEMICAL PATHOLOGY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4079,'PMH 1271000-00-31509','ASOI - TRAINING - O&G','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4080,'NLT 7007000-00-00000','LICENCES & REGISTRATION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4081,'NLT 3113125-00-38483','GOPC - BLDG CONSTRUCTION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4082,'NLT 1851000-00-38163','ADMIN. EXP. - PROFESSIONAL FEE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4083,'NLT 7007000-00-38950','PROFESSIONAL FEE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4084,'NLT 5206000-00-38673','ADMIN EXP - NON CAP - F&E (<$100,000)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4085,'NLT 9987000-00-39055','ADMIN. EXP.-OTHERS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4086,'NLT 9987000-00-52074','REFUND OF EMSD DEPOSIT (MISC CODE 5J47)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4087,'NLT 3113121-00-00000','GOPC-NKCC-OFFICIAL SIGNATURE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4088,'NLT 3113122-00-00000','GOPC-SKCC-OFFICIAL SIGNATURE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4089,'NLT 3113124-00-00000','GOPC-TYCH-OFFICIAL SIGNATURE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4090,'NLT 9981091-80-00000','HA STAFF OTHERS <SUB-TYPE OF SERVICES>','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4091,'NLT 5403000-00-00000','PHYSIOTHERAPY - PPI (SOPC)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4092,'NLT 5406000-00-00000','PHYSIOTHERAPY - PPI','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4093,'NLT 5453000-00-00000','PROSTHETICS & ORTHOTICS - PPI (SOPC)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4094,'NLT 9987000-00-00000','CLINICAL DEPARTMENT - PPI','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4095,'NLT 9987000-79-00000','GENERAL DONATION','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4096,'NLT 7027000-00-00000','HA-LIBRARY CLUB DEPOSIT & MEMBERSHIP FEE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (4097,'NLT 3003000-00-32002','ASOI-RI','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3276,'PMH 7357000-14-00000','BASIC SALARY - PERMANENT STAFF','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3277,'PMH 7807000-54-00000','BASIC SALARIES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3278,'PMH 9987000-54-00000','BASIC SALARY - CONTRACT STAFF','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3279,'PMH 2001000-04-00000','BASIC SALARIES - LEAVE ENCASHMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3280,'PMH 2001000-04-00000','HONORARIA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3281,'PMH 9987000-04-00000','HONORARIA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3282,'PMH 6756000-00-00000','ASOI - OTHERS (NURSING ADMINISTRATION)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3283,'PMH 6756000-00-31504','ASOI - TRAINING (NURSING ADMINSTRATION)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3284,'PMH 9987000-00-31802','ASOI - OTHERS-PS/BC/HS/AC','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3285,'PMH 9987000-00-31902','ASOI - PDPO','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3286,'PMH 7007000-00-00000','OTHR HOSPITAL SUPPLIES & EQUIP.','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3287,'PMH 9987000-00-00000','OTHER INCOME - INTEREST RECEIVED','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3288,'PMH 6116000-00-00000','REAGENTS READY FOR USE - BACTERIOLOGY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3289,'PMH 2001000-00-39214','SUTURS & CATGUT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3290,'PMH 6056000-00-38783','LABORATORY SUPPLIES & CONSUMABLES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3291,'PMH 5206000-00-38673','OTHER MED. SUPPLIES-OCCUPATIONAL THERAPY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3292,'PMH 7357000-00-00000','OTHER SUPPLIES- M COMPUTER-CONSUMABLES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3293,'PMH 7027000-00-38953','GENERAL PUBLICATION - PERIODICALS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3294,'PMH 7007000-00-38943','GAS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3295,'PMH 7007000-00-38948','UTILITIES-GAS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3296,'PMH 7007000-00-38969','R&M - BUILDINGS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3297,'PMH 7267000-00-38945','R&M-SERVICES BY GOVT. DEPT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3298,'PMH 1501000-00-38111','COURSE FEE-PROFESSIONAL EXAMINATION(MED)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3299,'GH 7357000-00-31011','ASOI - CANTEEN','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3300,'GH 1452000-00-64241','FUND FOR GENERAL INFIRMARY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3301,'GH 5156000-00-64253','FUND FOR MEDICAL SOCIAL SERVICES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3302,'GH 7357000-00-64259','FUND FOR CENTRAL MANAGEMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3303,'GH 9987000-00-00000','Wages In Lieu of notice','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3304,'GH 7357000-00-31911','ASOI - PERSONAL DATA (PRIVACY) ORDINANCE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3305,'ML 7007000-00-38801','Communication Charge - IDD Call','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3306,'ML 2202203-00-31581','ASOI - Training Rehabilition','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3307,'ML 9987000-00-00000','Other Miscellaneous Income','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3308,'ML 5103000-00-00000','Diet Medical Report','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3309,'KCH 9987000-00-00000','Wages in lieu of Notice','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3310,'TWH 7357000-00-31911','ASOI - PERSONAL DATA (PRIVACY) ORDINANCE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3311,'TWH 6026000-00-31581','ASOI-CLINICAL MENTOR SERVICE (O.T.)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3312,'TWH 1561003-00-64810','RENAL RESEARCH FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3313,'TWH 2251000-00-64819','EYE FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3314,'TWH 2251000-00-64819','DAY SURGERY FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3315,'TWH 7357000-00-64888','DONATION-STAFF WELFARE FUND','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3316,'TWH 9987000-00-00000','Exchange Gain','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3317,'PMH 7357000-00-39055','INCIDENTAL EXPENSES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3318,'PMH 9987001-79-00000','LKB-HA STAFF RECOVERY OF LOSS/DAMAGE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3319,'PMH 9987000-00-00000','RENTAL FEE - HYDROTHERAPY POOL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3320,'PMH 5456000-75-00000','LKB - P & O -SALE/HIRE CHARGES(OP-NEP)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3321,'PMH 5456000-80-00000','LKB-P&O-SALE/HIRE CHARGE (IP-HA)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3322,'PMH 5456000-85-00000','SKCP - PRO & ORTH PRIVATE OUTPATIENT CHA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3323,'PMH 9987000-79-00000','OTHER SALES UA','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3324,'PMH 7007000-00-38943','OTHER NON-STANDARD STATIONERY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3325,'KWH 9987000-00-38965','IDD Charges','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3326,'KWH 9987000-00-38946','Bank Charges','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3327,'KWH 9987000-00-31580','ASOI - Training','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3328,'KWH 9987000-00-31701','ASOI - Vending Machine','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3329,'KWH 9987000-00-31802','ASOI - KWH Library Income','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3330,'KWH 9987000-00-31812','ASOI - Research Project (PAED)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3331,'KWH 9987000-00-00000','Recovery of loss','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3332,'KWH 9987000-00-00000','Exchange diff','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3333,'KWH 9987000-00-00000','Disposal Gain','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3334,'KWH 9987000-00-00000','A/P Others','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3335,'KWH 9987000-00-00000','Deposit - Contractor','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3336,'KWH 9987000-00-64172','Donation to EMDU (T&D)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3337,'KWH 9987000-00-64240','Donation to Pathology (MS)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3338,'KWH 9987000-00-64340','Donation to SOPD (MS)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3339,'KWH 9987000-00-64912','Donation to Breast Centre (PSG)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3340,'KWH 9987000-00-39147','BMDR Donation','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3341,'KWH 9987000-00-39618','Patient Money','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3342,'KWH 9987000-00-35013','HACF Donation Box','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3343,'WTS 9987000-00-39147','PE Recharge','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3344,'WTS 9987000-00-38956','Other Medical Supplies (Others)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3345,'WTS 9987000-00-38945','Office & Household Supplies (Others)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3346,'WTS 9987000-00-38965','Communication (Others)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3347,'WTS 9987000-00-38953','Other Equipment','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3348,'WTS 9987000-00-00000','Medical Report & Records','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3349,'WTS 9987000-00-00000','Salary in Lieu of Notice','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3350,'WTS 9987000-00-00000','Payable to Sam Fund','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3351,'WTS 9987000-00-64140','Donation to DREC - 3A','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3352,'WTS 9987000-00-64160','Donation to DREC - 3C','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3353,'WTS 9987000-00-64170','Donation to DREC - 3F','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3354,'WTS 9987000-00-64180','Donation to DREC - 4EF','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3355,'DKC 5503000-00-00000','SPTH Medical Report','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3356,'FYK 7007000-00-00000','Auction Sale','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3357,'FYK 9987000-00-35013','C/A - HA Charitable Foundation','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3358,'GH 7357000-00-00000','Exchange Gain','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3359,'PWH 7607061-00-39202','Staff Welfare Fund - Spring Dinner / Christmas Party','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3360,'PWH 7007000-00-31102','ASOI - CARPARK (QUARTER)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3361,'PWH 7657005-00-31414','ASOI - NDH REHAB SHOP INCOME','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3362,'PWH 7007040-00-00000','ELECTICITY RECOVERY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3363,'PWH 7357001-00-64004','PWHCF Credit Card Donation','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3364,'PWH 1951004-00-64004','Donation to Department of OVA - Staff T&D','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3365,'PWH 7877005-00-64004','Donation to Nusing School','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3366,'PWH 9987000-00-35013','C/A - HA Charitable Foundation','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3367,'PWH 9987000-00-00000','Medical Report','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3368,'PWH 5206000-00-00000','Sale Of Occ Therapy Appliances','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3369,'PWH 1103000-00-00000','Sale/Hire Med Or Den Equ','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3370,'PWH 7707000-00-00000','Other Income - Cash Surplus','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3371,'PWH 9987000-00-00000','Other Income - Outside Work','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3372,'PWH 9987000-00-00000','Temp Receipt - Daily Cash Overage','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3373,'PWH 9987000-00-00000','Deposit - Tender','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3374,'PWH 6756000-00-00000','Nursing Transcript','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3375,'YCH 9987000-00-00000','Misc income','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3376,'YCH 3103129-00-00000','Misc income_Lady Trench','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3377,'OLM 9987000-00-00000','SUNDRY - RECOVERY OF TRAINING / LOSS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3378,'OLM 1501000-00-64052','DONATION - DIABETES SERVICE','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3379,'OLM 1352000-00-64060','DONATION BOX - PALLIATIVE CARE UNIT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3380,'OLM 5906000-00-64181','DONATION - PHARMACY','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3381,'OLM 9987000-00-64300','DONATION - PATIENT SERVICES','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3382,'OLM 7007000-00-32001','ASOI - VENUE RENTAL','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3383,'KCH 9987001-00-31001','ASOI Canteen - 31001','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3384,'KCH 5206000-00-31401','ASOI Circle C - 31401','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3385,'KCH 5203003-00-31408','ASOI WKPC Outward Scheme - 31408','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3386,'KCH 7007000-00-31510','ASOI Tobacco Contral Leadership Training Workshop - 31510','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3387,'KCH 7657000-00-31511','ASOI SWD ¦Ñ¦³©Ò¬°¬¡°Ê­p¹º - 31511','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3388,'KCH 5206000-00-31513','ASOI Tripartite Community Mental Health Training (OT) - 31513','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3389,'KCH 4155070-00-31602','ASOI Psy. Medical Services for LKRC - 31602','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3390,'KCH 2152015-00-31610','ASOI QEFMH - 31610','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3391,'KCH 2153002-00-31702','ASOI Vending Machine - EKPC - 31702','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3392,'KCH 7857000-00-31704','ASOI Vending Machine - Learning Centre - 31704','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3393,'KCH 5206000-00-31802','ASOI ERB - 31802','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3394,'KCH 5056000-00-31806','ASOI The HK Federation of Youth Groups - 31806','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3395,'KCH 7657000-00-31807','ASOI Equal Opportunities Commission - 31807','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3396,'KCH 9987001-00-32001','ASOI Promotion Stall - 32001','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3397,'KCH 2152013-00-00000','Team 3 - DON','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3398,'KCH 5206000-00-00000','OT - DON','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3399,'KCH 5056000-00-00000','CP -DON','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3400,'KCH 9987000-54-00000','PE recharge','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3401,'KCH 7007000-00-38947','Medical supplies - Admin','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3402,'KCH 7007000-00-38948','Office supplies (stationery)- Admin','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3403,'KCH 7007000-00-38949','Pager rental','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3404,'KCH 9987000-00-00000','Denture & dental fees (Sales of Equip)','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3405,'KCH 9987000-00-00000','Bank Charges','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3406,'NLT 7357000-00-38950','R & M - BUILDINGS','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3407,'NLT 7007000-00-38950','R & M - MEDICAL EQUIPMENT','T','T','F');
Insert into BILLING_SCHEMA.REF_TRANSACTION_REASON_CODE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,EXCLUDE_FROM_OUTSTANDING_BAL,FORCE_INPUT_NOTES) values (3408,'NLT 1501000-00-38111','COURSE FEE-PROFESSIONAL EXAMINATION(MED)','T','T','F');
