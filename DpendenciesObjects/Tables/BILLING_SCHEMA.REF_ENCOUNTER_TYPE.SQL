--------------------------------------------------------
--  DDL for Table REF_ENCOUNTER_TYPE
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(20 CHAR), 
	"DESCRIPTION" VARCHAR2(100 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR), 
	"NUMBERING_TEMPLATE_ID" NUMBER(9,0), 
	"VALDTE_FIN_CLASS_ENC_START_DTE" VARCHAR2(1 CHAR)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE"."NAME" IS 'The unique name of a particular reference table row.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE"."DESCRIPTION" IS 'The description of an encounter type.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE"."ACTIVE_FLAG" IS 'Whether a particular reference table row is active (T = yes, F = no). Only active reference table values will appear in screen combo boxes.';
   COMMENT ON TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE"  IS 'Stores encounter types.';
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSVC";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PATIENT_ENQUIRY";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_AS_ENCOUNTERTYPE
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_AS_ENCOUNTERTYPE" ON "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_ENCOUNTER_TYPE
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_ENCOUNTER_TYPE" BEFORE INSERT ON BILLING_SCHEMA.REF_ENCOUNTER_TYPE FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_ENCOUNTER_TYPE.NEXTVAL INTO :NEW.ID FROM DUAL; END; 

/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_ENCOUNTER_TYPE" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_ENCOUNTER_TYPE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" ADD CONSTRAINT "PK_REF_ENCOUNTER_TYPE" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_AS_ENCOUNTERTYPE"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);
--------------------------------------------------------
--  Ref Constraints for Table REF_ENCOUNTER_TYPE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_TYPE" ADD CONSTRAINT "FK_NUMBERING_TEMPLATE_ID" FOREIGN KEY ("NUMBERING_TEMPLATE_ID")
	  REFERENCES "BILLING_SCHEMA"."CONFIG_NUMBERING_TEMPLATE" ("ID") ENABLE;

REM INSERTING into BILLING_SCHEMA.REF_ENCOUNTER_TYPE
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_ENCOUNTER_TYPE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,NUMBERING_TEMPLATE_ID,VALDTE_FIN_CLASS_ENC_START_DTE) values (3,'A','A&E case','T',13,'T');
Insert into BILLING_SCHEMA.REF_ENCOUNTER_TYPE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,NUMBERING_TEMPLATE_ID,VALDTE_FIN_CLASS_ENC_START_DTE) values (4,'O','Out-Patient case','T',13,'F');
Insert into BILLING_SCHEMA.REF_ENCOUNTER_TYPE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,NUMBERING_TEMPLATE_ID,VALDTE_FIN_CLASS_ENC_START_DTE) values (1,'I','In-Patient case','T',13,'T');
Insert into BILLING_SCHEMA.REF_ENCOUNTER_TYPE (ID,NAME,DESCRIPTION,ACTIVE_FLAG,NUMBERING_TEMPLATE_ID,VALDTE_FIN_CLASS_ENC_START_DTE) values (2,'NP','Non patient case','T',14,'F');
