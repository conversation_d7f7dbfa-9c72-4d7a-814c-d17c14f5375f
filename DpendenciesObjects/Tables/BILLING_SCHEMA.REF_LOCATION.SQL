--------------------------------------------------------
--  DDL for Table REF_LOCATION
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_LOCATION" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(50 BYTE), 
	"TYPE_ID" NUMBER(9,0), 
	"LOCATION_NUMBER" VARCHAR2(25 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_LOCATION"."NAME" IS 'The unique name of a particular reference table row.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_LOCATION"."TYPE_ID" IS 'The type of a location eg ward, room. It is a linked to REF_LOCATION_TYPE table.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_LOCATION"."LOCATION_NUMBER" IS 'The unique identifier of a paticular location (eg ward 123).';
   COMMENT ON TABLE "BILLING_SCHEMA"."REF_LOCATION"  IS 'Stores location reference data.';
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCNBI";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCNBI";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCNBI";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PATIENT_ENQUIRY";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_LOCATION" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_LOCATION
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_LOCATION" ON "BILLING_SCHEMA"."REF_LOCATION" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_LOCATION
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_LOCATION" BEFORE INSERT ON BILLING_SCHEMA.REF_LOCATION FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_LOCATION.NEXTVAL INTO :NEW.ID FROM DUAL; END; 



















/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_LOCATION" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_LOCATION
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION" ADD CONSTRAINT "PK_REF_LOCATION" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_LOCATION"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION" MODIFY ("TYPE_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION" MODIFY ("LOCATION_NUMBER" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);
--------------------------------------------------------
--  Ref Constraints for Table REF_LOCATION
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION" ADD CONSTRAINT "FK_LOCATION_TYPE_REF" FOREIGN KEY ("TYPE_ID")
	  REFERENCES "BILLING_SCHEMA"."REF_LOCATION_TYPE" ("ID") ENABLE;







REM INSERTING into BILLING_SCHEMA.REF_LOCATION
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13702,'BURNS CENTRE',2,'PWH-5B','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13703,'WARD 5BC',2,'PWH-5BC','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13704,'BURNS CENTRE',2,'PWH-5BUR','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13705,'WARD 5D',2,'PWH-5D','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13706,'WARD 5M',2,'PWH-5M','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13707,'WARD 6A',2,'PWH-6A','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13708,'WARD 6B',1,'PWH-6B','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13709,'WARD 6D',2,'PWH-6D','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13710,'WARD 6H',2,'PWH-6H','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13711,'WARD 6L',2,'PWH-6L','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13712,'WARD 7CH',8,'PWH-7CH','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13713,'WARD 7D',2,'PWH-7D','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13714,'WARD 7E',2,'PWH-7E','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13715,'WARD 7EN',2,'PWH-7EN','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13716,'WARD 7F',2,'PWH-7F','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13717,'WARD 7FN',2,'PWH-7FN','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13718,'WARD 7H',2,'PWH-7H','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13719,'WARD 7L',2,'PWH-7L','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13720,'WARD 8DT',2,'PWH-8DT','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13721,'WARD 8E',2,'PWH-8E','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13722,'WARD 8EN',2,'PWH-8EN','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13723,'WARD 8F',2,'PWH-8F','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13724,'WARD 8FN',2,'PWH-8FN','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13725,'WARD 8H',2,'PWH-8H','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13726,'WARD 8K',2,'PWH-8K','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13727,'WARD 8M',2,'PWH-8M','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13728,'WARD 10H',2,'PWH-8MT','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13729,'WARD 9AV - OVERFLOW',2,'PWH-9AV','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13730,'WARD 9BV - OVERFLOW',2,'PWH-9BV','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13731,'WARD 9CA',2,'PWH-9CA','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13732,'WARD 9CV - OVERFLOW',2,'PWH-9CV','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13733,'WARD 9DA',2,'PWH-9DA','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13734,'WARD 9E',2,'PWH-9E','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13735,'WARD 10H',2,'PWH-9H','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13736,'WARD 10H',2,'PWH-9K','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13737,'WARD 9L',2,'PWH-9L','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13738,'WARD 10H',2,'PWH-9LA','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13739,'WARD 10H',2,'PWH-9LE','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13740,'WARD 9M',2,'PWH-9M','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13741,'WARD 10H',2,'PWH-9MA','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13742,'WARD 10H',2,'PWH-9ME','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13743,'AE Observation from Ward 8A',2,'PWH-AOM','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13744,'EYE DAY WARD',2,'PWH-EYED','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13745,'WARD GF',2,'PWH-GF','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13746,'INTENSIVE CARE UNIT',3,'PWH-ICU','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13747,'INTENSIVE CARE UNIT 4 FLOOR',3,'PWH-ICU4','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13748,'INTENSIVE CARE UNIT 5 FLOOR',3,'PWH-ICU5','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13749,'INTENSIVE CARE UNIT',2,'PWH-ICUB','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13750,'LITHOTRI & URO-INV CTR',2,'PWH-LUC','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13751,'RT Day Ward',2,'PWH-RTW','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13752,'Kowloon East Cataract Centre',2,'TKO-KECC','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13753,'Special O&T Clinic',2,'TKO-SORT','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13754,'O&T Day',2,'UCH-10AD','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13755,'Geriatric',2,'UCH-13AG','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13756,'Acute Stroke Unit',2,'UCH-13AS','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13757,'O&T',2,'UCH-9AD','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13758,'O&T Joint Replacement',2,'YCH-S9J','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13759,'O&T Joint Replacement',2,'YCH-N9J','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13760,'Home Haemodialysis Center',2,'QEH-C5L','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13761,'General Reh M Ward',7,'ML-GRMW','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13762,'WARD 11HD',2,'PWH-11HD','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13763,'TBCU Male Ward',2,'GH-1TCM','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13764,'Psychiatry (Acute)',2,'TPH-2A','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13765,'Psychiatry Admission',2,'TPH-2DL','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13766,'General Reh Female Ward',7,'ML-GRFW','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13767,'Med Day General',2,'QEH-G5G','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13768,'G5 Red Cross',2,'QEH-G5RC','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13769,'WARD 10AC',2,'PWH-10AC','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13770,'WARD 10AR - RENAL',2,'PWH-10AR','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13771,'WARD 10B',2,'PWH-10B','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13772,'WARD 10BC',2,'PWH-10BC','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13773,'WARD 10BT',2,'PWH-10BT','T');
Insert into BILLING_SCHEMA.REF_LOCATION (ID,NAME,TYPE_ID,LOCATION_NUMBER,ACTIVE_FLAG) values (13774,'WARD 10C',2,'PWH-10C','T');
