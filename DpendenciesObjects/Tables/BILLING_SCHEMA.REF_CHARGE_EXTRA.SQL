--------------------------------------------------------
--  DDL for Table REF_CHARGE_EXTRA
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_CHARGE_EXTRA" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(40 BYTE), 
	"EXTRA_GROUP_ID" NUMBER(9,0), 
	"EXTRA_ORDER" NUMBER(5,0), 
	"EXTRA_TYPE" VARCHAR2(1 BYTE), 
	"EXTRA_PARAMETER" VARCHAR2(2000 BYTE), 
	"DESCRIPTION" VARCHAR2(100 BYTE), 
	"ACTIVE_FLAG" VARCHAR2(1 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_CHARGE_EXTRA"."NAME" IS 'The unique name of a particular reference table row.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_CHARGE_EXTRA"."EXTRA_GROUP_ID" IS 'Group in which the Extra will be displayed on the entity screen. For example, Extras may be grouped by categories such as ?General? or ?Identification?. .';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_CHARGE_EXTRA"."EXTRA_ORDER" IS 'The order in which a particular extra field is to appear on the particular entity extra editor screen.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_CHARGE_EXTRA"."EXTRA_TYPE" IS 'Type gives a choice of Boolean, String, Integer, Decimal, Date, Date Time or Choice for the input field. Determines the control on the entity screen used to edit the extra field.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_CHARGE_EXTRA"."EXTRA_PARAMETER" IS 'Parameter: Where the type of an extra field is Choice, parameter refers to the values that are able to be selected for that field. Each choice must be separated by a semi-colon (;), and the type field must be ?C? (Choice).';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_CHARGE_EXTRA"."DESCRIPTION" IS 'The description of a charge extra field.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_CHARGE_EXTRA"."ACTIVE_FLAG" IS 'Whether a particular charge extra field appears on the charges extra screen (T = yes, F = no).';
   COMMENT ON TABLE "BILLING_SCHEMA"."REF_CHARGE_EXTRA"  IS 'Stores extra reference information for CHARGES';
  GRANT ALTER ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" TO "PBRCOPER";
--------------------------------------------------------
--  DDL for Index UNIQUE_CHARGE_EXTRA_NAME
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."UNIQUE_CHARGE_EXTRA_NAME" ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" ("NAME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_REF_CHARGE_EXTRA
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_CHARGE_EXTRA" ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index REF_CHARGE_EXTRA_N99
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."REF_CHARGE_EXTRA_N99" ON "BILLING_SCHEMA"."REF_CHARGE_EXTRA" (UPPER("NAME")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_CHARGE_EXTRA
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_CHARGE_EXTRA" BEFORE INSERT ON BILLING_SCHEMA.REF_CHARGE_EXTRA FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_CHARGE_EXTRA.NEXTVAL INTO :NEW.ID FROM DUAL; END; 





/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_CHARGE_EXTRA" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_CHARGE_EXTRA
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_CHARGE_EXTRA" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_CHARGE_EXTRA" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_CHARGE_EXTRA" MODIFY ("EXTRA_ORDER" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_CHARGE_EXTRA" MODIFY ("EXTRA_TYPE" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_CHARGE_EXTRA" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);


REM INSERTING into BILLING_SCHEMA.REF_CHARGE_EXTRA
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (1,'Invoice Deferral',31,1,'C','Do Not Invoice;Do Not Issue Invoice','Options to delay or prevent the charge from being invoiced','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (2,'From External Source',31,2,'S',null,'Charge duplicated from an external source','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (3,'GL Pay Code',32,5,'S',null,'GL Pay Code','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (4,'GL Ward Class',32,6,'S',null,'GL Ward Class','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (5,'GL Ward Code',32,7,'S',null,'GL Ward Code','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (6,'GL Case Type',32,8,'S',null,'GL Case Type','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (7,'GL Hospital Code',32,1,'S',null,'GL Hospital Code','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (8,'GL EIS Service Type',32,9,'S',null,'GL EIS Service Type','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (9,'GL EIS Specialty',32,10,'S',null,'GL EIS Specialty','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (10,'GL EIS Sub-specialty',32,11,'S',null,'GL EIS Sub-specialty','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (11,'GL OPAS Shroff Group',32,12,'S',null,'GL OPAS Shroff Group','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (12,'GL OPAS Specialty',32,13,'S',null,'GL OPAS Specialty','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (13,'GL CBNS Centre Code',32,14,'S',null,'GL CBNS Centre Code','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (14,'GL Service Hospital Code',32,3,'S',null,'GL Service Hospital Code','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (15,'GL Encounter Misc',38,1,'S',null,'GL Encounter Misc','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (16,'GL Service Misc',38,2,'S',null,'GL Service Misc','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (17,'GL Misc',32,2,'S',null,'GL Misc','T');
Insert into BILLING_SCHEMA.REF_CHARGE_EXTRA (ID,NAME,EXTRA_GROUP_ID,EXTRA_ORDER,EXTRA_TYPE,EXTRA_PARAMETER,DESCRIPTION,ACTIVE_FLAG) values (18,'GL Charge Code',32,4,'S',null,'GL Charge Code','T');
