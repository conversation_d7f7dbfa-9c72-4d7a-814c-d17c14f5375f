--------------------------------------------------------
--  DDL for Table REF_FIN_CLASS
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_FIN_CLASS" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(20 CHAR), 
	"DESCRIPTION" VARCHAR2(100 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR), 
	"ALLOW_MANUAL_SELECTION" VARCHAR2(1 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_FIN_CLASS"."NAME" IS 'The unique name of a particular reference table row.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_FIN_CLASS"."DESCRIPTION" IS 'The description of a financial class type.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_FIN_CLASS"."ACTIVE_FLAG" IS 'Whether a particular reference table row is active (T = yes, F = no). Only active reference table values will appear in screen combo boxes.';
   COMMENT ON TABLE "BILLING_SCHEMA"."REF_FIN_CLASS"  IS 'Stores financial class reference data.';
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSVC";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PATIENT_ENQUIRY";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_FIN_CLASS" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_FIN_CLASS
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_FIN_CLASS" ON "BILLING_SCHEMA"."REF_FIN_CLASS" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_FIN_CLASS
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_FIN_CLASS" BEFORE INSERT ON BILLING_SCHEMA.REF_FIN_CLASS FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_FIN_CLASS.NEXTVAL INTO :NEW.ID FROM DUAL; END; 



















/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_FIN_CLASS" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_FIN_CLASS
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_FIN_CLASS" ADD CONSTRAINT "PK_REF_FIN_CLASS" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_FIN_CLASS"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_FIN_CLASS" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_FIN_CLASS" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_FIN_CLASS" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_FIN_CLASS" MODIFY ("ALLOW_MANUAL_SELECTION" NOT NULL ENABLE);



REM INSERTING into BILLING_SCHEMA.REF_FIN_CLASS
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (3,'DG2','EXISTING MPS 33A-44 (DEPENDANT OF GS)','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (4,'DG3','BELOW MPS 33A (DEPENDANT OF GS)','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (5,'DGS','DEPENDANT / SPOUSE OF GOVERNMENT SERVANT / PENSIONER','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (6,'DH1','DEPENDENT OF HA STAFF (BAND 1)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (7,'DH2','DEPENDENT OF HA STAFF (BAND 2 & 3)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (8,'DH3','DEPENDENT OF HA STAFF (BAND 4, 5 & 6)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (9,'DHA','DEPENDENT OF HA STAFF (OTHERS)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (10,'DP','DEPENDANT OF PENSIONER','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (11,'DRS','DEPENDANT OF RESIDENT SITE STAFF','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (12,'EP1','ELIGIBLE PERSON','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (13,'EP2','BRITISH PASSPORT HOLDER','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (14,'EP3','NON-ENTITLED PERSON WHOSE SPOUSE IS HKID HOLDER','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (15,'EP4','UNDER 11 YEARS OF AGE NON-ENTITLED CHILD WHOSE PARENT(S) IS/ARE HKID CARD HOLDER','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (16,'EP5','PERSON ACCOMPANYING AN EP PATIENT AND OCCUPYING A BED','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (17,'EX','PATIENTS EXEMPTED BY GOVERNMENT OR LAW','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (18,'GAZ','PATIENTS EXEMPTED UNDER GAZETTE','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (19,'GOV','GS/PENSIONER/LEGCO & DEPENDANT/SPOUSE (UNCONFIRMED)','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (20,'GRP','GOVERNMENT RELATED PARTIES / DEPENDANTS','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (21,'GS','GOVERNMENT SERVANT','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (22,'GS1','EXISTING MPS 45 OR ABOVE (GS)','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (23,'GS2','EXISTING MPS 33A-44 (GS)','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (24,'GS3','BELOW MPS 33A (GS)','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (25,'HA','HA STAFF(OTHERS)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (26,'HA1','HA STAFF (BAND 1)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (27,'HA2','HA STAFF (BAND 2 & 3 OR NON-BAND 1 MEMBERS OF MED, NURSING AND AH PROF)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (28,'HA3','HA STAFF (BAND 4, 5 & 6)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (29,'IFD','PLAGUE/CHOLERA/YELLOW FEVER','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (30,'II','ILLEGAL IMMIGRANT','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (31,'ME','MAJOR EVENTS (FEES EXEMPTED BY THE GOVERNMENT)','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (32,'NE1','OVERSEAS PASSPORT HOLDER, EXCLUDE NE2 / NE3 / NE4','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (33,'NE2','CHINESE PASSPORT/TWO-WAY PERMIT HOLDER (MAINLAND CHINA),EXCLUDE NE1 / NE3 / NE4','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (34,'NE3','NON ELIGIBLE PERSON WHOSE SPOUSE IS HKID HOLDER','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (35,'NE4','NON-ELIGIBLE CHILD UNDER AGE OF 18, WHOSE PARENTS IS/ARE HKID CARD HOLDER','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (36,'NE5','PERSON ACCOMPANYING A NEP PATIENT AND OCCUPYING A BED','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (37,'NE6','REFUGEE & ASYLUM SEEKERS','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (38,'NE7','NON-REFOULEMENT CLAIMANTS','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (39,'NE9','UN-IDENTIFIED PERSON','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (40,'NEP','UN-IDENTIFIED PERSON','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (41,'OTH','OTHERS','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (42,'P','PENSIONER','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (43,'PA','PUBLIC ASSISTANCE','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (44,'PIP','PRIVATE IN-PATIENT','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (45,'POP','PRIVATE OUTPATIENT','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (46,'RH1','RETIRED HA STAFF (BAND 1)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (47,'RH2','RETIRED HA STAFF (BAND 2 & 3 OR NON-BAND 1 MEMBERS OF MED, NURSING AND AH PROF)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (48,'RH3','RETIRED HA STAFF (BAND 4, 5 & 6)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (49,'RHA','RETIRED HA STAFF (OTHERS)','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (50,'RSS','RESIDENT SITE STAFF','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (51,'SPO','NON-HOSPITAL STAFF OF PARENT ORGANISATION','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (52,'SR1','SPOUSE OF RETIRED HA STAFF (BAND 1)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (53,'SR2','SPOUSE OF RETIRED HA STAFF (BAND 2 & 3)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (54,'SR3','SPOUSE OF RETIRED HA STAFF (BAND 4, 5 & 6)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (55,'SRH','SPOUSE OF RETIRED HA STAFF (OTHERS)','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (56,'SS','NON-HA HOSPITAL STAFF OF SUBVENTED HOSPITAL','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (57,'TDG','DEPENDANT / SPOUSE OF GS/PENSIONER [GF181/TRY447]','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (58,'TGR','GOVERNMENT RELATED PARTIES / DEPENDANTS [LS181]','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (59,'TGS','GOVERNMENT SERVANT [GF181]','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (60,'TP','PENSIONER [TRY447]','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (61,'TPA','TEMP PUBLIC ASSISTANCE','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (62,'UID','CLAIMED HKID CARD HOLDER','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (63,'UNI','HKU/CU CLINICIANS','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (64,'VBP','VIETNAMESE BOAT PEOPLE','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (65,'VR','VIETNAMESE REFUGEE','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (66,'WV','WAR VICTIM','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (67,'HAS','HA STAFF, RETIREE, & DEPENTANT/SPOUSE (UNCONFIRMED)','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (68,'TD1','DEPENTANT/SPOUSE OF HA STAFF/RETIREE (Band 1)-HA181/2','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (69,'TD2','DEPENDANT/SPOUSE of HA STAFF/RETIREE(Band2&3)-HA181/2','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (70,'TD3','DEPENDANT/SPOUSE of HA STAFF/RETIREE(Band4,5,6)-HA181/2','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (71,'TH1','HA STAFF (BAND 1) [HA181/2]','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (72,'TH2','HA STAFF-BAND2/3 OR NON-BAND1 MED,NURSING &AH [HA181/2]','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (73,'TH3','HA STAFF (BAND 4, 5 & 6) [HA181/2]','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (74,'TR1','RETIRED HA STAFF (BAND 1) [HA181/2]','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (75,'TR2','RETIRED HA-BAND2/3 OR NON-BAND1 MED,NURSING&AH -HA181/2','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (76,'TR3','RETIRED HA STAFF (BAND 4, 5 & 6) [HA181/2]','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (1,'CC','PRISONER OR RESIDENCE OF BOYS/GIRLS HOME','T','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (2,'DG1','EXISTING MPS 45 OR ABOVE,DEPENDANT OF GS','F','F');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (77,'TEP','ELIGIBLE PERSON (UNCONFIRMED)','T','T');
Insert into BILLING_SCHEMA.REF_FIN_CLASS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,ALLOW_MANUAL_SELECTION) values (78,'TNE','UN-IDENTIFIED PERSON (UNCONFIRMED)','T','T');
