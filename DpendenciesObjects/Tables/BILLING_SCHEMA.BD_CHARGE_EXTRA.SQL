--------------------------------------------------------
--  DDL for Table BD_CHARGE_EXTRA
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."BD_CHARGE_EXTRA" 
   (	"ENTITY_ID" NUMBER(15,0), 
	"EXTRA_NAME_ID" NUMBER(9,0), 
	"VALUE" VARCHAR2(2000 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE_EXTRA"."ENTITY_ID" IS 'The charge id that is linked to a particular extra field value.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE_EXTRA"."EXTRA_NAME_ID" IS 'The actual extra that is to appear on the entity extras screen. This is a link back to the entity extra reference tables.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE_EXTRA"."VALUE" IS 'The value of an extra field stored against the particular entity (ie charge).';
   COMMENT ON TABLE "BILLING_SCHEMA"."BD_CHARGE_EXTRA"  IS 'Stores the extra references related to charges.';
  GRANT ALTER ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index PK_CHARGE_EXTRA
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_CHARGE_EXTRA" ON "BILLING_SCHEMA"."BD_CHARGE_EXTRA" ("ENTITY_ID", "EXTRA_NAME_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  Constraints for Table BD_CHARGE_EXTRA
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE_EXTRA" MODIFY ("ENTITY_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE_EXTRA" MODIFY ("EXTRA_NAME_ID" NOT NULL ENABLE);
