--------------------------------------------------------
--  DDL for Table PATIENT_ACCOUNT_LOG
--------------------------------------------------------

  CREATE TABLE "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" 
   (	"LOG_DATETIME" TIMESTAMP (6), 
	"LOG_MSG" VARCHAR2(2000 CHAR)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
  GRANT READ ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRC_RW_ROLE";
  GRANT READ ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRCPRDDBADM";
  GRANT INSERT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRCPRDDBADM";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRCPRDDBADM";
  GRANT DELETE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRCPRDDBBG";
  GRANT INSERT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRCPRDDBBG";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index IDX_PATIENT_ACCOUNT_LOG
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."IDX_PATIENT_ACCOUNT_LOG" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_LOG" (TRUNC("LOG_DATETIME")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
