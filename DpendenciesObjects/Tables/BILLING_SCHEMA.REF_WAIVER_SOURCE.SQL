--------------------------------------------------------
--  DDL for Table REF_WAIVER_SOURCE
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_WAIVER_SOURCE" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(15 CHAR), 
	"DESCRIPTION" VARCHAR2(200 CHAR), 
	"PRIORITY" NUMBER(9,0), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR), 
	"UPDATE_FLAG" VARCHAR2(1 CHAR), 
	"REVERSE_ON_UPDATE_FLAG" VARCHAR2(1 BYTE), 
	"APPLY_TO_OUTSTANDING_AMOUNT" VARCHAR2(1 BYTE), 
	"REJECT_FLAG" VARCHAR2(1 BYTE), 
	"APPROVAL_FLAG" VARCHAR2(1 BYTE), 
	"TOTAL_APPLIED_FLAG" VARCHAR2(1 BYTE), 
	"UPDATE_FINCLASS_ID" NUMBER(9,0), 
	"CSSA_FLAG" VARCHAR2(1 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCNBI";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCNBI";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCNBI";
  GRANT READ ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_WAIVER_SOURCE
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_WAIVER_SOURCE" ON "BILLING_SCHEMA"."REF_WAIVER_SOURCE" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_WAIVER_SOURCE
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_WAIVER_SOURCE" BEFORE INSERT ON BILLING_SCHEMA.REF_WAIVER_SOURCE FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_WAIVER_SOURCE.NEXTVAL INTO :NEW.ID FROM DUAL; END; 



















/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_WAIVER_SOURCE" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_WAIVER_SOURCE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_SOURCE" ADD CONSTRAINT "PK_REF_WAIVER_SOURCE" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_WAIVER_SOURCE"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_SOURCE" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_SOURCE" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_SOURCE" MODIFY ("DESCRIPTION" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_SOURCE" MODIFY ("PRIORITY" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_SOURCE" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_SOURCE" MODIFY ("UPDATE_FLAG" NOT NULL ENABLE);
--------------------------------------------------------
--  Ref Constraints for Table REF_WAIVER_SOURCE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_SOURCE" ADD CONSTRAINT "REF_WAIVER_SOURCE_FIN_CLASS_FK" FOREIGN KEY ("UPDATE_FINCLASS_ID")
	  REFERENCES "BILLING_SCHEMA"."REF_FIN_CLASS" ("ID") ENABLE;



REM INSERTING into BILLING_SCHEMA.REF_WAIVER_SOURCE
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (1,'MSW-Manual','MSW Waiver input via Legacy PBRC',3,'T','F','F',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (2,'MSW-Ewaiver','MSW Waiver issued via EWS',3,'T','F','T',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (3,'Manual-PostSARS','Post SARS Waiver',2,'T','F','F',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (4,'Manual-HCE','Waiver for special incident (e.g. Swine Flu) [Not synchronized to EWS]',0,'F','T','F',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (5,'CSSA (OLD)','CSSA Waiver (before 18 May 2007)',1,'T','F','F',null,'F','F','F',43,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (6,'CSSA','CSSA Waiver (on/after 18 May 2007)',1,'T','F','F','T','F','F','F',43,'T');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (7,'Special','Special waiver for HAZC',2,'F','F','F',null,'F','F','F',43,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (8,'Manual','HA Waiver input via IPAS/OPAS/Legacy PBRC',3,'T','F','F',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (9,'Manual-SWD476','One-off waiver issed by Social Welfare Department',3,'T','T','F',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (10,'Manual-HA(G)12','Manual waiver issued by MSW during EWS downtime',3,'T','T','F',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (11,'Manual-HA1958','One-off waiver for GOPC service approved by HCE designated officer',3,'T','T','F',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (12,'Manual-Refugee','Letter from UNHCR to be presented during registration. Waiver ref no. adopts prefix "RF" plus the ref no. of the letter.',3,'T','T','F',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (13,'Manual-OBS Pkg','Waiver for OBS Package charge only',3,'T','T','F',null,'T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (14,'Manual-HA','Waiver issued by HA',3,'T','T','F','F','T','T','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (18,'Special Waiver','Special Waiver',2,'T','F','F','F','T','F','T',null,'F');
Insert into BILLING_SCHEMA.REF_WAIVER_SOURCE (ID,NAME,DESCRIPTION,PRIORITY,ACTIVE_FLAG,UPDATE_FLAG,REVERSE_ON_UPDATE_FLAG,APPLY_TO_OUTSTANDING_AMOUNT,REJECT_FLAG,APPROVAL_FLAG,TOTAL_APPLIED_FLAG,UPDATE_FINCLASS_ID,CSSA_FLAG) values (16,'Other waiver','Other waiver',3,'T','T','F','F','T','T','T',null,'F');
