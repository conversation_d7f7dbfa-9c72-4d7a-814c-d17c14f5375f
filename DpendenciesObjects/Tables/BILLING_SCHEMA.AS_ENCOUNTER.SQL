--------------------------------------------------------
--  DDL for Table AS_ENCOUNTER
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."AS_ENCOUNTER" 
   (	"ID" NUMBER(15,0), 
	"ENCOUNTER_NUMBER" VARCHAR2(30 CHAR), 
	"RECIPIENT_ENTITY_DETAIL_ID" NUMBER(15,0), 
	"ENCOUNTER_TYPE_ID" NUMBER(9,0), 
	"START_DATE_TIME" TIMESTAMP (6), 
	"END_DATE_TIME" TIMESTAMP (6), 
	"ADMISSION_ELECTION_ID" NUMBER(9,0), 
	"SEPARATION_ELECTION_ID" NUMBER(9,0), 
	"EXPECTED_LOS_DAYS" NUMBER(9,0), 
	"PATIENT_CATEGORY_ID" NUMBER(9,0), 
	"REFERRAL_SOURCE_ID" NUMBER(9,0), 
	"SEPARATION_STATUS_ID" NUMBER(9,0), 
	"ORGANISATIONAL_ID" NUMBER(9,0), 
	"BANNER_MESSAGE" VARCHAR2(300 CHAR), 
	"PROVISIONAL_DRG_CODE_ID" NUMBER(9,0), 
	"DRG1_CODE_ID" NUMBER(9,0), 
	"DRG2_CODE_ID" NUMBER(9,0), 
	"DRG3_CODE_ID" NUMBER(9,0), 
	"ENCOUNTER_STATUS_ID" NUMBER(9,0), 
	"MERGED_TO_ENCOUNTER_ID" NUMBER(15,0), 
	"CANCELLED" VARCHAR2(1 CHAR), 
	"APPROVAL_STATUS_ID" NUMBER(9,0), 
	"APPROVAL_STATUS_NOTES" VARCHAR2(200 CHAR), 
	"NEXT_MILESTONE_APPROVAL_DATE" TIMESTAMP (0), 
	"TOTAL_WAIVER_AMOUNT_APPLIED" NUMBER(20,2), 
	"TOTAL_WAIVER_AMOUNT_APPROVED" NUMBER(20,2), 
	"WAIVER_APPROVED_BY" NUMBER(9,0), 
	"APP_REJECTION_EFFECTIVE_DATE" TIMESTAMP (0), 
	"LEGACY_WAIVER_AMOUNT_APPLIED" NUMBER(20,2), 
	"LEGACY_WAIVER_AMOUNT_APPROVED" NUMBER(20,2)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."ENCOUNTER_NUMBER" IS 'The unique identifier of a particular encounter.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."RECIPIENT_ENTITY_DETAIL_ID" IS 'The recipient entity associated with an encounter.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."ENCOUNTER_TYPE_ID" IS 'The type of encounter. This is a FK link to the REF_ENCOUNTER_TYPE table.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."START_DATE_TIME" IS 'The start date and time of an encounter.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."END_DATE_TIME" IS 'The end date and time for an encounter also known as the separation date.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."ADMISSION_ELECTION_ID" IS 'Indicates how a recipient opted to be admitted to hospital (eg Public v Private).';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."SEPARATION_ELECTION_ID" IS 'Stores the different type of patient that can enter a hospital. Examples include ?Public? and ?Private?. The choices available for this field are defined in the ''Separation Election'' Reference table. This also represents the patient choice of payment method for the encounter at the time of admission.  .';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."EXPECTED_LOS_DAYS" IS 'The expected length of stay in hospital for an encounter.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."PATIENT_CATEGORY_ID" IS 'The patient category for a particular encounter. This field is a FK to the REF_PATIENT_CATEGORY_TABLE.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."REFERRAL_SOURCE_ID" IS 'Where a recipient was referred from for a particular encounter. It is a FK to the REF_REFERRAL_SOURCE reference table.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."SEPARATION_STATUS_ID" IS 'This field displays the seperation status of the encounter. For example at the end of each encounter a recipient may be discharged, deceased or relocated.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."BANNER_MESSAGE" IS 'Stores a general message about an encounter for display to a billing clerk.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."PROVISIONAL_DRG_CODE_ID" IS 'The provisional DRG code for a particular encounter. This is a FK to the REF_DRG table.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."DRG1_CODE_ID" IS 'A FK link to a particular DRG value stored in the REF_DRG table.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."DRG2_CODE_ID" IS 'A FK link to a particular DRG value stored in the REF_DRG table.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."DRG3_CODE_ID" IS 'A FK link to a particular DRG value stored in the REF_DRG table.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER"."ENCOUNTER_STATUS_ID" IS 'The current status of an encounter. This is a FK back to the REF_ENCOUNTER_STATUS table.';
   COMMENT ON TABLE "BILLING_SCHEMA"."AS_ENCOUNTER"  IS 'Activity store encounter details table';
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCSVC";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCSVC";
  GRANT ALTER ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PBRC_REFUND";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "FCSBILLING";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_SEP_STATUS
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_SEP_STATUS" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("SEPARATION_STATUS_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_DRG2_REF_DRG
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_DRG2_REF_DRG" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("DRG2_CODE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_ORGANISATION
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_ORGANISATION" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("ORGANISATIONAL_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_STATUS_ENCOUNTER
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_STATUS_ENCOUNTER" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("ENCOUNTER_STATUS_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index UNIQUE_AS_ENCOUNTER
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."UNIQUE_AS_ENCOUNTER" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("ENCOUNTER_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_REF_REFERRAL_SRC
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_REF_REFERRAL_SRC" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("REFERRAL_SOURCE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_PAT_CATEG
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_PAT_CATEG" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("PATIENT_CATEGORY_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_TYPE_ENCOUNTER
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_TYPE_ENCOUNTER" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("ENCOUNTER_TYPE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_DRG1_REF_DRG
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_DRG1_REF_DRG" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("DRG1_CODE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_DRG3_REF_DRG
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_DRG3_REF_DRG" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("DRG3_CODE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_PROV_REF_DRG
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_PROV_REF_DRG" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("PROVISIONAL_DRG_CODE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_AS_ENCOUNTER
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_AS_ENCOUNTER" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_SEP_ELECTION
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_SEP_ELECTION" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("SEPARATION_ELECTION_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index HA_AS_ENC_RECIPIENT_ID_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."HA_AS_ENC_RECIPIENT_ID_IDX" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("RECIPIENT_ENTITY_DETAIL_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENC_START_DATE_TIME_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENC_START_DATE_TIME_IDX" ON "BILLING_SCHEMA"."AS_ENCOUNTER" ("START_DATE_TIME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index HA_AS_ENC_CASE_NUM_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."HA_AS_ENC_CASE_NUM_IDX" ON "BILLING_SCHEMA"."AS_ENCOUNTER" (LTRIM(SUBSTR("ENCOUNTER_NUMBER",INSTR("ENCOUNTER_NUMBER",'-')+1))) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENCOUNTER_CASE_NUMBER_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENCOUNTER_CASE_NUMBER_IDX" ON "BILLING_SCHEMA"."AS_ENCOUNTER" (SUBSTR("ENCOUNTER_NUMBER",INSTR("ENCOUNTER_NUMBER",'-')+1)) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENC_ENCOUNTER_NUMBER_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENC_ENCOUNTER_NUMBER_IDX" ON "BILLING_SCHEMA"."AS_ENCOUNTER" (UPPER("ENCOUNTER_NUMBER")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_AS_ENCOUNTER
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_AS_ENCOUNTER" BEFORE INSERT ON BILLING_SCHEMA.AS_ENCOUNTER
FOR EACH ROW 
BEGIN 

--Simon070831: OALA medium solution: For in-patient only
IF :NEW.ENCOUNTER_TYPE_ID = 1 
then
  INSERT INTO billing_schema.cssa_waiver_check_txn (hkid, create_datetime, hospital, encounter_number, check_from_date, check_to_date, type, priority, process_status , process_datetime, hkid_ind, dob, exact_dob, nep_search_key,process_queue_id)
  --SELECT case when ed.entity_number like 'U%' and length(trim(ed.entity_number)) <> 8 then ed.entity_number else ' ' || ed.entity_number end hkid --**********: bug fix hkid space
  SELECT case when substr(ed.entity_number,2,1) not in ('0','1','2','3','4','5','6','7','8','9') then ed.entity_number else ' ' || ed.entity_number end hkid --**********: bug fix hkid space
  , SYSTIMESTAMP, ORG.CODE, :NEW.ENCOUNTER_NUMBER, :NEW.START_DATE_TIME
  , case when :NEW.END_DATE_TIME is null then sysdate 
        else :NEW.END_DATE_TIME end
  , 'Online', 2, 'I', null, case when ed.entity_number like 'U%' and length(trim(ed.entity_number)) <> 8 then 'N' else 'Y' end
  , nvl(r.DATE_OF_BIRTH, to_date('19000101','yyyymmdd')), case when r.IS_EXACT_DATE_OF_BIRTH = 'T' then 'Y' else 'N' end
  , ( select ID_NUMBER from billing_schema.as_recipient_identifier  where RECIPIENT_ENTITY_DETAIL_ID = ed.id and type_id = 28 ) NEP_SEARCH_KEY
  ,(select mod(ed.id,value) + 1 from BILLING_SCHEMA.central_waiver_config where key = 'ONLINE_QUEUE_NO') 
  FROM BILLING_SCHEMA.AS_ENTITY_DETAIL ED, BILLING_SCHEMA.OS_ORGANISATION ORG, BILLING_SCHEMA.AS_RECIPIENT r
  WHERE ED.ID = :NEW.RECIPIENT_ENTITY_DETAIL_ID
  AND ORG.ID = :NEW.ORGANISATIONAL_ID
  and r.ENTITY_DETAIL_ID = ed.id
  and exists (select 1 from billing_schema.cssa_waiver_declared a where a.hkid = ed.entity_number)
  
  ;
END IF;
--SF070831: OALA medium solution
END;




/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_AS_ENCOUNTER" ENABLE;
--------------------------------------------------------
--  Constraints for Table AS_ENCOUNTER
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER" MODIFY ("ENCOUNTER_NUMBER" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER" MODIFY ("RECIPIENT_ENTITY_DETAIL_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER" MODIFY ("START_DATE_TIME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER" MODIFY ("ORGANISATIONAL_ID" NOT NULL ENABLE);
