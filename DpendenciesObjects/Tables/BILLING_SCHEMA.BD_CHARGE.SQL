--------------------------------------------------------
--  DDL for Table BD_CHARGE
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."BD_CHARGE" 
   (	"ID" NUMBER(15,0), 
	"CODE" VARCHAR2(70 BYTE), 
	"DESCRIPTION1" VARCHAR2(200 CHAR), 
	"DESCRIPTION2" VARCHAR2(200 CHAR), 
	"DESCRIPTION3" VARCHAR2(200 CHAR), 
	"DESCRIPTION4" VARCHAR2(200 CHAR), 
	"ENCOUNTER_ID" NUMBER(15,0), 
	"SERVICE_START_DATE_TIME" TIMESTAMP (6), 
	"SERVICE_END_DATE_TIME" TIMESTAMP (6), 
	"QUANTITY" NUMBER(9,0), 
	"CHARGE_TYPE" VARCHAR2(1 CHAR), 
	"CHARGE_SUB_TYPE_ID" NUMBER(9,0), 
	"MANUAL_CHARGE_FLAG" VARCHAR2(1 CHAR), 
	"ORDER_NUMBER" VARCHAR2(20 CHAR), 
	"ALLOCATION_CODE" VARCHAR2(30 BYTE), 
	"REQUEST_REFERRING_PROVIDER_ID" NUMBER(9,0), 
	"PAYEE_PROVIDER_ID" NUMBER(9,0), 
	"SERVICING_PROVIDER_ID" NUMBER(9,0), 
	"REQUEST_REFERRAL_DATE" TIMESTAMP (0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE"."CODE" IS 'The name given to a particular charge. This will usually be the code of the service that produced the charge.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE"."DESCRIPTION1" IS 'The description of a particular charge code. This should be the service code that the charge was derived from.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE"."ENCOUNTER_ID" IS 'Represents a FK link to a particular encounter.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE"."SERVICE_START_DATE_TIME" IS 'The date and time of the start date on a charge.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE"."SERVICE_END_DATE_TIME" IS 'The end date and time for a particular charge.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE"."QUANTITY" IS 'The quantity of the service consumed by the recipient during that encounter. .';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE"."CHARGE_TYPE" IS 'A classification of a charge. Values are E for Existing, O for Other and P for Proposed. .';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE"."CHARGE_SUB_TYPE_ID" IS 'A further classification of a charge if it has a charge type of O (Other). .';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE"."MANUAL_CHARGE_FLAG" IS 'Flags whether a charge has been manually created by a billing clerk and not by the charge determination engine.';
   COMMENT ON TABLE "BILLING_SCHEMA"."BD_CHARGE"  IS 'Stores charge objects related to encounters.';
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCSVC";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_CHARGE" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_CHARGE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_CHARGE_SUB_TYPE
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_CHARGE_SUB_TYPE" ON "BILLING_SCHEMA"."BD_CHARGE" ("CHARGE_SUB_TYPE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENCOUNTER_CHARGE
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENCOUNTER_CHARGE" ON "BILLING_SCHEMA"."BD_CHARGE" ("ENCOUNTER_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_BD_CHARGE
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_BD_CHARGE" ON "BILLING_SCHEMA"."BD_CHARGE" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_CHARGE_D_N99
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_CHARGE_D_N99" ON "BILLING_SCHEMA"."BD_CHARGE" ("ENCOUNTER_ID", "ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  Constraints for Table BD_CHARGE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE" MODIFY ("CODE" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE" MODIFY ("ENCOUNTER_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE" MODIFY ("QUANTITY" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE" MODIFY ("CHARGE_TYPE" NOT NULL ENABLE);
