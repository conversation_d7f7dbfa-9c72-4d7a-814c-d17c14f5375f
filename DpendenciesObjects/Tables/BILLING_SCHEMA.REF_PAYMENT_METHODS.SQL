--------------------------------------------------------
--  DDL for Table REF_PAYMENT_METHODS
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" 
   (	"ID" NUMBER(9,0), 
	"TYPE" VARCHAR2(2 CHAR), 
	"NAME" VARCHAR2(20 CHAR), 
	"DESCRIPTION" VARCHAR2(100 CHAR), 
	"ACTIVE_FLAG" CHAR(1 CHAR), 
	"IS_DEFAULT" CHAR(1 CHAR), 
	"DISPLAY" VARCHAR2(1 CHAR), 
	"REFUND_PAYMENT_METHOD_ID" NUMBER(9,0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRA<PERSON> 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
  GRANT READ ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "QLIK_SA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "YLM182";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PATIENT_ENQUIRY";
  GRANT READ ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_PAYMENT_METHODS
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_PAYMENT_METHODS" ON "BILLING_SCHEMA"."REF_PAYMENT_METHODS" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_PAYMENT_METHOD
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_PAYMENT_METHOD" BEFORE INSERT ON BILLING_SCHEMA.REF_PAYMENT_METHODS FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_PAYMENT_METHOD.NEXTVAL INTO :NEW.ID FROM DUAL; END; 



















/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_PAYMENT_METHOD" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_PAYMENT_METHODS
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" ADD CONSTRAINT "PK_REF_PAYMENT_METHODS" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_PAYMENT_METHODS"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" ADD CONSTRAINT "TYPE" CHECK ("TYPE" IN ('CA', 'CH', 'CC', 'EL', 'SM', 'HW')) ENABLE NOVALIDATE;
  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" MODIFY ("TYPE" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" MODIFY ("IS_DEFAULT" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" MODIFY ("DISPLAY" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" MODIFY ("REFUND_PAYMENT_METHOD_ID" NOT NULL ENABLE);
--------------------------------------------------------
--  Ref Constraints for Table REF_PAYMENT_METHODS
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_PAYMENT_METHODS" ADD CONSTRAINT "FK_REFUND_PAYMENT_METHOD_ID" FOREIGN KEY ("REFUND_PAYMENT_METHOD_ID")
	  REFERENCES "BILLING_SCHEMA"."REF_PAYMENT_METHODS" ("ID") ENABLE;




REM INSERTING into BILLING_SCHEMA.REF_PAYMENT_METHODS
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (1,'EL','A1','ATM-JETCO','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (2,'CA','CA','CASH','T','T','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (3,'CH','CQ','CHEQUE','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (4,'CC','CR','CREDIT CARD','T','F','T',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (5,'EL','EP','EPS','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (6,'CA','FC','FOREIGN CURRENCY','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (7,'EL','HO','HEAD OFFICE','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (8,'SM','OC','OCTOPUS','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (9,'CH','PC','POSTAL CHEQUE','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (10,'EL','PS','PPS','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (11,'EL','S1','CONVENIENCE STORE-7-ELEVEN','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (12,'EL','AE',null,'F','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (13,'EL','DO','Deferred Payment','F','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (14,'EL','DI','Deferred Payment for Debtor Injury on Duty','F','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (15,'EL','DG','Deferred Payment for Debtor DH','F','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (16,'EL','DP','Deferred Payment for Debtor PCFB','F','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (17,'EL','DM','Deferred Payment for MACAU','F','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (18,'EL','DT','Deferred Payment for Debtor TWG','F','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (19,'EL','DS','Deferred Payment for Debtor SAM_FUND','F','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (21,'EL','BI','Direct bank-in','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (20,'EL','CF','Receipt for incorrect refund','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (81,'EL','OH','OTHER HOSPITAL','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (82,'EL','RS','REFUND OF SUNDRY RECEIPT','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (162,'EL','QR','ALIPAY','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (163,'EL','QR','WECHAT PAY','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (222,'EL','FP','FPS settlement via QR code printed on billing documents','T','F','T',222);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (103,'HW','EO','OCTOPUS MACHINE','T','F','T',2);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (105,'HW','EC','CREDIT CARD MACHINE - VISA','F','F','F',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (107,'HW','EC','CREDIT CARD MACHINE - MASTERCARD','F','F','F',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (109,'HW','EC','CREDIT CARD MACHINE - CUP','F','F','F',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (111,'HW','EC','CREDIT CARD MACHINE - JCB','F','F','F',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (262,'EL','OT','Others','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (282,'EL','QT','Tap & Go (Standalone mode)','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (283,'EL','MT','Mobile App-Tap & Go','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (284,'EL','QB','BoC Pay (Standalone mode)','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (285,'EL','MB','Mobile App-BoC Pay','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (286,'EL','QF','FPS (Standalone mode) – counter/kiosk','T','F','F',222);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (287,'EL','EF','FPS (Integrated mode) – counter/kiosk','T','F','F',222);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (288,'EL','ET','Tap & Go (Integrated mode)','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (289,'EL','EB','BoC Pay (Integrated mode)','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (242,'EL','MF','Mobile App-FPS','T','F','T',242);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (122,'EL','MC','Mobile App-Credit Card','T','F','F',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (123,'EL','MO','Mobile App-Octopus','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (124,'EL','MA','Mobile App-Alipay','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (125,'EL','MW','Mobile App-WeChat Pay','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (126,'EL','AP','Mobile App-Apple Pay','T','F','F',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (143,'HW','NEW_EC','VISA','T','F','T',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (145,'HW','NEW_EC','MASTERCARD','T','F','T',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (147,'HW','NEW_EC','CUP','T','F','T',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (149,'HW','NEW_EC','JCB','T','F','T',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (150,'EL','MJ','Mobile App-JETCO Pay','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (151,'EL','MG','Mobile App-Google Pay','T','F','F',4);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (183,'HW','EW','ALIPAY','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (185,'HW','EW','WECHAT PAY','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (187,'HW','NEW_EW','ALIPAY','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (189,'HW','NEW_EW','WECHAT PAY','T','F','T',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (202,'EL','QA','ALIPAY','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (203,'EL','QW','WECHAT PAY','T','F','F',3);
Insert into BILLING_SCHEMA.REF_PAYMENT_METHODS (ID,TYPE,NAME,DESCRIPTION,ACTIVE_FLAG,IS_DEFAULT,DISPLAY,REFUND_PAYMENT_METHOD_ID) values (204,'EL','MP','Mobile App-PayMe','T','F','F',3);
