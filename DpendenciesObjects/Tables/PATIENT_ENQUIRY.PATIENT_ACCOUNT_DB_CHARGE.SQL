--------------------------------------------------------
--  DDL for Table PATIENT_ACCOUNT_DB_CHARGE
--------------------------------------------------------

  CREATE TABLE "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" 
   (	"ENCOUNTER_NUMBER" VARCHAR2(30 CHAR), 
	"CHARGE_ID" NUMBER(15,0), 
	"SERVICE_CODE" VARCHAR2(70 CHAR), 
	"SERVICE_START_DATE_TIME" TIMESTAMP (6), 
	"SERVICE_DESC_ENG" VARCHAR2(200 CHAR), 
	"SERVICE_DESC_CHI" VARCHAR2(200 CHAR), 
	"EIS_SPECIALTY" VARCHAR2(20 CHAR), 
	"CHARGE_AMT" NUMBER(20,2), 
	"ADJUSTED_AMT" NUMBER(20,2), 
	"BILLED_TOTAL" NUMBER(20,2), 
	"WRITEOFF_TOTAL" NUMBER(20,2), 
	"WAIVER_TOTAL" NUMBER(20,2), 
	"OTHER_AMT" NUMBER(20,2), 
	"PAYMENT_AMT_BY_PAYOR" NUMBER(20,2), 
	"PAYMENT_AMT_BY_OTHERS" NUMBER(20,2), 
	"INVOICE_NUMBER" VARCHAR2(30 CHAR), 
	"INVOICE_ID" NUMBER(15,0), 
	"INVOICE_HOSP" VARCHAR2(6 CHAR), 
	"BILL_TO_PAYOR" VARCHAR2(30 CHAR), 
	"LAST_ADJUSTMENT" TIMESTAMP (6), 
	"LAST_ALLOCATION" TIMESTAMP (6), 
	"INVOICE_ISSUE_DATE" TIMESTAMP (6), 
	"INVOICE_STATUS" VARCHAR2(1 CHAR), 
	"LAST_PAYMENT_DATE_BY_PAYOR" TIMESTAMP (6)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "HKHA";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "ROSUP";
  GRANT READ ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRC_RW_ROLE";
  GRANT READ ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRCPRDDBADM";
  GRANT INSERT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRCPRDDBADM";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRCPRDDBADM";
  GRANT DELETE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRCPRDDBBG";
  GRANT INSERT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRCPRDDBBG";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PATIENT_ACCOUNT_DB_CHARGE_IDX1
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE_IDX1" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" ("ENCOUNTER_NUMBER", "LAST_PAYMENT_DATE_BY_PAYOR") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index PATIENT_ACCOUNT_DB_CHARGE_IDX2
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE_IDX2" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" ("BILL_TO_PAYOR") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index PATIENT_ACCOUNT_DB_CHARGE_IDX3
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE_IDX3" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_CHARGE" ("ENCOUNTER_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
