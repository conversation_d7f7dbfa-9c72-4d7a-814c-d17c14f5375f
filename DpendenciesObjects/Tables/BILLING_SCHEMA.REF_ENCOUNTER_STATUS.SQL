--------------------------------------------------------
--  DDL for Table REF_ENCOUNTER_STATUS
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(20 CHAR), 
	"DESCRIPTION" VARCHAR2(100 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR), 
	"STATUS_COLOUR" VARCHAR2(1 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS"."NAME" IS 'The unique name of a particular reference table row.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS"."DESCRIPTION" IS 'The description of an encounter status type.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS"."ACTIVE_FLAG" IS 'Whether a particular reference table row is active (T = yes, F = no). Only active reference table values will appear in screen combo boxes.';
   COMMENT ON TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS"  IS 'Stores statuses of the encounters.';
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "YLM182";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PATIENT_ENQUIRY";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_ENCOUNTER_STATUS
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_ENCOUNTER_STATUS" ON "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_ENCOUNTER_STATUS
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_ENCOUNTER_STATUS" BEFORE INSERT ON BILLING_SCHEMA.REF_ENCOUNTER_STATUS FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_ENCOUNTER_STATUS.NEXTVAL INTO :NEW.ID FROM DUAL; END; 
/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_ENCOUNTER_STATUS" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_ENCOUNTER_STATUS
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" ADD CONSTRAINT "PK_REF_ENCOUNTER_STATUS" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_ENCOUNTER_STATUS"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_ENCOUNTER_STATUS" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);



REM INSERTING into BILLING_SCHEMA.REF_ENCOUNTER_STATUS
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_ENCOUNTER_STATUS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,STATUS_COLOUR) values (3,'CANCELLED','Cancelled','T','R');
Insert into BILLING_SCHEMA.REF_ENCOUNTER_STATUS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,STATUS_COLOUR) values (1,'OPEN','Open','T','B');
Insert into BILLING_SCHEMA.REF_ENCOUNTER_STATUS (ID,NAME,DESCRIPTION,ACTIVE_FLAG,STATUS_COLOUR) values (2,'CLOSED','Closed','T','B');
