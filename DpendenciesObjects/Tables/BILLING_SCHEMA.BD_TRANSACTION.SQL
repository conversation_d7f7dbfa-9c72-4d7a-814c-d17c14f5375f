--------------------------------------------------------
--  DDL for Table BD_TRANSACTION
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."BD_TRANSACTION" 
   (	"ID" NUMBER(15,0), 
	"WRITEOFF_ENCOUNTER_REF" NUMBER(15,0), 
	"TRANSACTION_ORG_ID" NUMBER(9,0), 
	"TRANSACTION_NUMBER" VARCHAR2(30 CHAR), 
	"TRANS_TYPE" VARCHAR2(3 BYTE), 
	"CURRENCY_ID" NUMBER(9,0), 
	"PAYOR_ENTITY_DETAIL_ID" NUMBER(15,0), 
	"TOTAL_AMOUNT" NUMBER(38,2), 
	"UNDEBITED_AMOUNT" NUMBER(38,2), 
	"DATE_TIME_ENACTED" TIMESTAMP (6), 
	"DATE_TIME_ENTERED" TIMESTAMP (6), 
	"PAYMENT_METHOD_ID" NUMBER(9,0), 
	"CUSTOMER_ACCOUNT_NAME" VARCHAR2(100 CHAR), 
	"CHEQUE_NUMBER" VARCHAR2(50 CHAR), 
	"CHEQUE_DRAWER" VARCHAR2(50 CHAR), 
	"BANK" VARCHAR2(50 CHAR), 
	"BRANCH" VARCHAR2(50 CHAR), 
	"CARD_NUMBER" VARCHAR2(25 CHAR), 
	"CREDIT_CARD_EXPIRY_DATE" TIMESTAMP (0), 
	"CREDIT_CARD_ADDITIONAL_FIELD" VARCHAR2(3 CHAR), 
	"NOTES" VARCHAR2(500 CHAR), 
	"FOREIGN_CCY_AMOUNT" NUMBER(38,2), 
	"REF_TRANSACTION_ID" NUMBER(15,0), 
	"TRANSACTION_SUBTYPE_ID" NUMBER(9,0), 
	"TRANSACTION_REASON_ID" NUMBER(9,0), 
	"USER_ID" NUMBER(9,0), 
	"HOST_ID" NUMBER(9,0), 
	"SOURCE_FILE" VARCHAR2(50 CHAR), 
	"REF_ENTITY_TYPE" VARCHAR2(1 CHAR), 
	"REF_ENTITY_ID" NUMBER(15,0), 
	"REF_WAIVER_ID" NUMBER(15,0), 
	"REF_WAIVER_SNAPSHOT_ID" NUMBER(15,0), 
	"TENDERED_AMOUNT" NUMBER(38,2), 
	"CHANGE_AMOUNT" NUMBER(38,2), 
	"REF_RECEIPT_ID" VARCHAR2(25 CHAR), 
	"STATUS" VARCHAR2(1 CHAR), 
	"AUTHORISING_USER_ID" NUMBER(9,0), 
	"IS_POSTPONED" VARCHAR2(1 CHAR) DEFAULT 'F', 
	"RECEIVING_BANK_ID" NUMBER(9,0), 
	"DEPOSITOR_NAME" VARCHAR2(40 CHAR), 
	"DEPOSITOR_ADDRESS_1" VARCHAR2(40 CHAR), 
	"DEPOSITOR_ADDRESS_2" VARCHAR2(40 CHAR), 
	"DEPOSITOR_ADDRESS_3" VARCHAR2(40 CHAR), 
	"INCLUDE_LINKED_ENCOUNTERS" VARCHAR2(1 CHAR), 
	"REMINDER_DATE" TIMESTAMP (6), 
	"GL_POSTING_BATCH_NUMBER" VARCHAR2(30 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_TRANSACTION"."WRITEOFF_ENCOUNTER_REF" IS 'The encounter id of the charge components affected by a write off transaction';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_TRANSACTION"."TRANSACTION_ORG_ID" IS 'The encounter organisation id of the charge components affected by a write off transaction';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_TRANSACTION"."UNDEBITED_AMOUNT" IS 'The total amount of a live write off credit transaction';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_TRANSACTION"."FOREIGN_CCY_AMOUNT" IS 'The total amount column in the home currency';
  GRANT SELECT ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCSVC";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_TRANSACTION" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index FK_BD_TRANSACTION_CURRENCY
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_BD_TRANSACTION_CURRENCY" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("CURRENCY_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_BD_TRANSACTION_REF_TRANS_ID
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_BD_TRANSACTION_REF_TRANS_ID" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("REF_TRANSACTION_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_BD_TRANSACTION
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_BD_TRANSACTION" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_BD_TRANSACTION_PAYOR
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_BD_TRANSACTION_PAYOR" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("PAYOR_ENTITY_DETAIL_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index UNIQUE_BD_TRANSACTION_NUMBER
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."UNIQUE_BD_TRANSACTION_NUMBER" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("TRANSACTION_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_TRANSACTION_TRANS_TYPE_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_TRANSACTION_TRANS_TYPE_IDX" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("TRANS_TYPE", "REF_WAIVER_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index HA_BD_TXN_DTM_ENTERED_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."HA_BD_TXN_DTM_ENTERED_IDX" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("DATE_TIME_ENTERED") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index HA_BD_TXN_TXN_SUBTYPE_ID_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."HA_BD_TXN_TXN_SUBTYPE_ID_IDX" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("TRANSACTION_SUBTYPE_ID") 
  PCTFREE 10 INITRANS 16 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_TRANSACTION_HOST_ID
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_TRANSACTION_HOST_ID" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("HOST_ID", "DATE_TIME_ENTERED") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_TRANS_REF_ENTITY_ID_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_TRANS_REF_ENTITY_ID_IDX" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("REF_ENTITY_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_TRANSACTION_USER_ID
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_TRANSACTION_USER_ID" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("USER_ID", "DATE_TIME_ENTERED") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_TRANSACTION_DTM_ENACTED_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_TRANSACTION_DTM_ENACTED_IDX" ON "BILLING_SCHEMA"."BD_TRANSACTION" ("DATE_TIME_ENACTED") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_TRANSACTION_NUMB_UPPER_IDX
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."BD_TRANSACTION_NUMB_UPPER_IDX" ON "BILLING_SCHEMA"."BD_TRANSACTION" (UPPER("TRANSACTION_NUMBER")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_BD_TRANSACTION_I
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_BD_TRANSACTION_I" 
    AFTER INSERT OR UPDATE -- UPDATE IF MERGE HKID
    ON BILLING_SCHEMA.BD_TRANSACTION
    FOR EACH ROW
    DECLARE

    BEGIN
        INSERT INTO BILLING_SCHEMA.PADB_ACTIVITY_LOG
        ( TRANSACTION_ID, LOG_SOURCE, LOG_DATE_TIME, LOG_PROCESS_STATUS )
        VALUES (
            CASE
                WHEN :NEW.REF_TRANSACTION_ID IS NOT NULL THEN :NEW.REF_TRANSACTION_ID
                ELSE :NEW.ID
            END,
            'BD_TRANSACTION', SYSTIMESTAMP, 'I'
        );
    END;


/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_BD_TRANSACTION_I" ENABLE;
--------------------------------------------------------
--  Constraints for Table BD_TRANSACTION
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."BD_TRANSACTION" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANSACTION" MODIFY ("TRANSACTION_NUMBER" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANSACTION" MODIFY ("TRANS_TYPE" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANSACTION" MODIFY ("PAYOR_ENTITY_DETAIL_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANSACTION" MODIFY ("TOTAL_AMOUNT" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANSACTION" MODIFY ("DATE_TIME_ENACTED" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANSACTION" MODIFY ("DATE_TIME_ENTERED" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANSACTION" MODIFY ("IS_POSTPONED" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANSACTION" ADD CONSTRAINT "UNIQUE_BD_TRANSACTION_NUMBER" UNIQUE ("TRANSACTION_NUMBER")
  USING INDEX "BILLING_SCHEMA"."UNIQUE_BD_TRANSACTION_NUMBER"  ENABLE;
