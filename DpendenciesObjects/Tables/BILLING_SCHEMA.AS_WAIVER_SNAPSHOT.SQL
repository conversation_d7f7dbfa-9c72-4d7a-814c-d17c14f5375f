--------------------------------------------------------
--  DDL for Table AS_WAIVER_SNAPSHOT
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" 
   (	"ID" NUMBER(15,0), 
	"WAIVER_ID" NUMBER(15,0), 
	"REFERENCE_ID" VARCHAR2(20 CHAR), 
	"RECIPIENT_ID" NUMBER(15,0), 
	"ISSUING_PARTY" VARCHAR2(20 CHAR), 
	"ISSUING_OFFICER" VARCHAR2(20 CHAR), 
	"ISSUING_ORG_ID" NUMBER(9,0), 
	"PAYOR_ID" NUMBER(15,0), 
	"SOURCE_ID" NUMBER(9,0), 
	"TYPE_ID" NUMBER(9,0), 
	"EFFECTIVE_DATE" TIMESTAMP (6), 
	"EXPIRY_DATE" TIMESTAMP (6), 
	"EFFECTIVE_STOP_DATE" TIMESTAMP (6), 
	"FIN_CLASS_ID" NUMBER(9,0), 
	"TARGET_ENCOUNTER_ID" NUMBER(15,0), 
	"TARGET_INVOICE_ID" NUMBER(15,0), 
	"CHARGE_CODE_FILTER" VARCHAR2(2000 CHAR), 
	"AMOUNT_TO_BE_PAID" NUMBER(38,2), 
	"ENCOUNTER_DAILY_LIMIT" NUMBER(38,2), 
	"PERCENT_TO_BE_PAID" NUMBER(4,2), 
	"CAP_AMOUNT" NUMBER(38,2), 
	"NOTES" VARCHAR2(200 CHAR), 
	"SNAPSHOT_DATE_TIME" TIMESTAMP (6), 
	"SNAPSHOT_CREATED_USER_ID" NUMBER(9,0), 
	"CSSA_CUSTOM_KEY" VARCHAR2(20 CHAR), 
	"CSSA_CASE_KEY" VARCHAR2(20 CHAR), 
	"DELETED_FLAG" VARCHAR2(1 CHAR), 
	"CAP_TYPE" VARCHAR2(1 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
  GRANT ALTER ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" TO "PBRCOPER";
--------------------------------------------------------
--  DDL for Index PK_AS_WA_SNAP
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_AS_WA_SNAP" ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index HA_AS_WAIVER_SNAP_REC_ID_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."HA_AS_WAIVER_SNAP_REC_ID_IDX" ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" ("RECIPIENT_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_WAIVER_SNAPSHOT_WVR_ID_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT_WVR_ID_IDX" ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" ("WAIVER_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index HA_AS_WAIVER_SNAP_REF_ID_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."HA_AS_WAIVER_SNAP_REF_ID_IDX" ON "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" ("REFERENCE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_AS_WA_SNAP
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_AS_WA_SNAP" BEFORE INSERT ON BILLING_SCHEMA.AS_WAIVER_SNAPSHOT FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_AS_WA_SNAP.NEXTVAL INTO :NEW.ID FROM DUAL; END; 





/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_AS_WA_SNAP" ENABLE;
--------------------------------------------------------
--  Constraints for Table AS_WAIVER_SNAPSHOT
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("WAIVER_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("REFERENCE_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("RECIPIENT_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("SOURCE_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("TYPE_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("EFFECTIVE_DATE" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("EXPIRY_DATE" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("SNAPSHOT_DATE_TIME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("SNAPSHOT_CREATED_USER_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_WAIVER_SNAPSHOT" MODIFY ("DELETED_FLAG" NOT NULL ENABLE);
