--------------------------------------------------------
--  DDL for Table AS_ENTITY_DETAIL
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."AS_ENTITY_DETAIL" 
   (	"ID" NUMBER(15,0), 
	"ENTITY_NUMBER" VARCHAR2(30 CHAR), 
	"ENTITY_SECONDARY_ID" VARCHAR2(30 CHAR), 
	"SURNAME" VARCHAR2(30 CHAR), 
	"GIVEN_NAMES" VARCHAR2(50 CHAR), 
	"DISPLAY_NAME" VARCHAR2(100 CHAR), 
	"INITIALS" VARCHAR2(10 CHAR), 
	"TITLE_ID" NUMBER(9,0), 
	"ORGANISATION_NAME" VARCHAR2(60 CHAR), 
	"IS_ORGANISATION_FLAG" VARCHAR2(1 CHAR), 
	"BANNER_MESSAGE" VARCHAR2(300 CHAR), 
	"ORGANISATIONAL_ID" NUMBER(9,0), 
	"MERGED_TO_ENTITY_ID" NUMBER(15,0), 
	"MOBILE_PAYMENT" VARCHAR2(1 BYTE), 
	"IS_CORP_DEBTOR_FLAG" VARCHAR2(1 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."ENTITY_NUMBER" IS 'A unique identifier of a particular entity.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."ENTITY_SECONDARY_ID" IS 'A unique secondary identifier of a particular entity.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."SURNAME" IS 'The surname of a particular entity.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."GIVEN_NAMES" IS 'The given name of a particular entity. This entity could be a recipient, payor, fin resp.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."DISPLAY_NAME" IS 'The display name of a particular entity. This entity could be a recipient, payor, fin resp.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."INITIALS" IS 'The initials of a particular entity.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."TITLE_ID" IS 'The type of title for a particular entity. This is a FK to the REF_TITLE_TYPE table.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."ORGANISATION_NAME" IS 'If an entity is flagged as being an organisation, then this field stores the name of the organisation.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."IS_ORGANISATION_FLAG" IS 'Whether a particular entity is infact an organisation and not an individual.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENTITY_DETAIL"."BANNER_MESSAGE" IS 'Stores a general message about an entity for display to a billing clerk.';
   COMMENT ON TABLE "BILLING_SCHEMA"."AS_ENTITY_DETAIL"  IS 'Stores activity store entity details.';
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCSVC";
  GRANT ALTER ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "FCSBILLING";
  GRANT READ ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "FCSBILLING";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index PK_AS_ENTITY_DETAIL
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_AS_ENTITY_DETAIL" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ORG_AS_ENTITY_DETAIL
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ORG_AS_ENTITY_DETAIL" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" ("ORGANISATIONAL_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_TITLE_TYPE_AS_ENTITY_DETAIL
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_TITLE_TYPE_AS_ENTITY_DETAIL" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" ("TITLE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index UNIQUE_ENTITY_DETAIL
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."UNIQUE_ENTITY_DETAIL" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" ("ENTITY_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENT_DETAIL_TRIM_HKID_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENT_DETAIL_TRIM_HKID_IDX" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" (SUBSTR("ENTITY_NUMBER",(-7))) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENTITY_DETAIL_SEC_ID_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENTITY_DETAIL_SEC_ID_IDX" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" ("ENTITY_SECONDARY_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENT_DETL_SURNAME_SOUNDX_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENT_DETL_SURNAME_SOUNDX_IDX" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" (SOUNDEX("SURNAME")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENT_DETL_GVNNAME_SOUNDX_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENT_DETL_GVNNAME_SOUNDX_IDX" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" (SOUNDEX("GIVEN_NAMES")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENT_DETL_ENTITY_NUMBER_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENT_DETL_ENTITY_NUMBER_IDX" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" (UPPER("ENTITY_NUMBER")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENT_DETAIL_SURNAME_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENT_DETAIL_SURNAME_IDX" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" (UPPER("SURNAME")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENT_DETAIL_GIVEN_NAMES_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENT_DETAIL_GIVEN_NAMES_IDX" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" (UPPER("GIVEN_NAMES")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENT_DETAIL_ORG_NAME_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENT_DETAIL_ORG_NAME_IDX" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" (UPPER("ORGANISATION_NAME")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index AS_ENT_DETL_DISPLAY_NAME_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."AS_ENT_DETL_DISPLAY_NAME_IDX" ON "BILLING_SCHEMA"."AS_ENTITY_DETAIL" (UPPER("DISPLAY_NAME")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  Constraints for Table AS_ENTITY_DETAIL
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."AS_ENTITY_DETAIL" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENTITY_DETAIL" MODIFY ("ORGANISATIONAL_ID" NOT NULL ENABLE);
