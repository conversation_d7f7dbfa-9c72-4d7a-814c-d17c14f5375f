--------------------------------------------------------
--  DDL for Table BD_OPERATION
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."BD_OPERATION" 
   (	"ID" NUMBER(15,0), 
	"OPERATION_NUMBER" VARCHAR2(30 CHAR), 
	"OPERATION_TYPE" VARCHAR2(3 CHAR), 
	"REASON_ID" NUMBER(9,0), 
	"USER_ID" NUMBER(9,0), 
	"NOTES" VARCHAR2(500 BYTE), 
	"DATE_TIME_RECEIVED" TIMESTAMP (6), 
	"DATE_TIME_ENTERED" TIMESTAMP (6)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
  GRANT SELECT ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCSVC";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_OPERATION" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_OPERATION" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_OPERATION" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index FK_BD_OPERATION_USER_ID
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_BD_OPERATION_USER_ID" ON "BILLING_SCHEMA"."BD_OPERATION" ("USER_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_BD_OPERATION
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_BD_OPERATION" ON "BILLING_SCHEMA"."BD_OPERATION" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_BD_OPERATION_REASON_ID
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_BD_OPERATION_REASON_ID" ON "BILLING_SCHEMA"."BD_OPERATION" ("REASON_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_OPERATION_OPT_TYPE_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_OPERATION_OPT_TYPE_IDX" ON "BILLING_SCHEMA"."BD_OPERATION" ("OPERATION_TYPE", "ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_OPERATION_OPT_NO_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_OPERATION_OPT_NO_IDX" ON "BILLING_SCHEMA"."BD_OPERATION" ("OPERATION_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index HA_BD_OP_DTM_ENTERED_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."HA_BD_OP_DTM_ENTERED_IDX" ON "BILLING_SCHEMA"."BD_OPERATION" ("DATE_TIME_ENTERED") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_OPERATION_N99
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_OPERATION_N99" ON "BILLING_SCHEMA"."BD_OPERATION" ("OPERATION_TYPE", "DATE_TIME_RECEIVED") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  Constraints for Table BD_OPERATION
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION" MODIFY ("OPERATION_TYPE" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION" MODIFY ("USER_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION" MODIFY ("DATE_TIME_RECEIVED" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION" MODIFY ("DATE_TIME_ENTERED" NOT NULL ENABLE);
