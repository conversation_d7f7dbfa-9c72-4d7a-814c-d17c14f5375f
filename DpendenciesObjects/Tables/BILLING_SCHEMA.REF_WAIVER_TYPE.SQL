--------------------------------------------------------
--  DDL for Table REF_WAIVER_TYPE
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_WAIVER_TYPE" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(15 CHAR), 
	"DESCRIPTION" VARCHAR2(200 CHAR), 
	"DEFAULT_FILTER" VARCHAR2(2000 CHAR), 
	"FIXED_AMT_ALLOWED_FLAG" VARCHAR2(1 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR), 
	"APPLY_SAME_DAY_IP_ENCOUNTERS" VARCHAR2(1 CHAR), 
	"INCLUDE_TAX" VARCHAR2(1 BYTE), 
	"VALIDATE_MAX_DAYS" NUMBER(9,0), 
	"ALLOW_OVERRIDE_MAX_DAYS" VARCHAR2(1 BYTE), 
	"REQUIRE_TARGET_ENTITY" VARCHAR2(1 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCNBI";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_WAIVER_TYPE
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_WAIVER_TYPE" ON "BILLING_SCHEMA"."REF_WAIVER_TYPE" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_WAIVER_TYPE
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_WAIVER_TYPE" BEFORE INSERT ON BILLING_SCHEMA.REF_WAIVER_TYPE FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_WAIVER_TYPE.NEXTVAL INTO :NEW.ID FROM DUAL; END; 



















/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_WAIVER_TYPE" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_WAIVER_TYPE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_TYPE" ADD CONSTRAINT "PK_REF_WAIVER_TYPE" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_WAIVER_TYPE"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_TYPE" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_TYPE" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_TYPE" MODIFY ("DESCRIPTION" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_TYPE" MODIFY ("FIXED_AMT_ALLOWED_FLAG" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_WAIVER_TYPE" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);



REM INSERTING into BILLING_SCHEMA.REF_WAIVER_TYPE
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (34,'8a','Waiver valid for a period for inpatient services (age >= 65)','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (35,'8b','Waiver valid for a period for all A&E, SOPD, FMSC, GOPC (including both episodic and non-episodic appts), day hospitals and community services, injection and dressing (age >= 65)','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (36,'7a','Valid for a period for inpatient services (age < 65)','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (37,'7b','Valid for a period for all A&E, SOPD, FMSC, GOPC (Non-episodic appointments), day hospitals , community services, injection and dressing (age < 65)','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (1,'01','One-off waiver for in-patient services','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','T','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (2,'02','waiver valid for a period for all A&E, SOPD, FMSC, GOPC (Non-episodic case), day hospitals and community services, injection and dressing','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (3,'03','One-off waiver for services at A&E/SOPD/FMSC/ GOPC (Non-episodic/episodic case)/day hospitals/community services/others ','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-ATTD-INJ--M,+N-DRUG-ANTIVIRAL','T','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (4,'04','Waiver valid for a period for injection and dressing','+N-ATTD-INJ-','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (5,'05','Waiver valid for a period for in-patient admitted and discharged in the same day','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','F','T','T',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (6,'06','waiver valid for a period for all A&E, SOPD, FMSC, GOPC (including both episodic case and non-episodic case), day hospitals and community services, injection and dressing','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (9,'M1','period waiver for in-patient services','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (10,'M2','period waiver for out-patient services','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','T','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (11,'H1','One-off waiver issued by HA for in-patient services','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,','T','F','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (12,'H2','One-off waiver issued by HA for out-patient services','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','T','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (13,'C1','Period waiver for all services(Old CSSA)','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (14,'P1','Period waiver with cert. number "PS0001" for non-A&E services and only for OPAS EIS service types (PSC/APSC/IPSC/PPSC)','+N-ATTD-APSC--M,+N-ATTD-IPSC--M,+N-ATTD-PSC--M,+N-ATTD-PPSC--M,+N-DRUG-SOPD-OP-M,+N-DRUG-ANTIVIRAL','F','T','F','F',null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (15,'C3','Period waiver with cert. prefix "HAZC" for all services','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (16,'C2','Period waiver for all services(New CSSA)','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','F','T','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (17,'DC','One-off one day waiver with cert.# "DC_WAIVER" for OPAS deferred payment means "DC" for FMSC refill clinic (No need to store in EWS)','+N-ATTD-,+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,','T','F','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (18,'K1','Period waiver for IP [HA(G)12]','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','F','T','F',null,30,'T','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (19,'K2','Period waiver for OP [HA(G)12]','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','F','T','F',null,1,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (20,'F1','One-off waiver for IP [HA(G)12]','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','T','T','F',null,30,'T','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (21,'F2','One-off waiver for OP [HA(G)12]','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+I-PATH-,+I-RAD-,+I-DTP-,+N-DRUG-ANTIVIRAL','T','T','F',null,1,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (22,'F9','One-off waiver for OP [HA1958]','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','T','T','F',null,1,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (23,'K3','Period waiver for IP [HCE]','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,','F','F','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (24,'K4','Period waiver for OP [HCE]','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-','F','F','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (25,'F3','One-off waiver for IP [HCE]','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,','T','F','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (26,'F4','One-off waiver for OP [HCE]','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-','T','F','F',null,null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (27,'K5','Period waiver for IP [Refugee]','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','F','T','F',null,30,'T','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (28,'K6','Period waiver for OP [Refugee]','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','F','T','F',null,1,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (29,'F5','One-off waiver for IP [Refugee]','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','T','T','F',null,30,'T','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (30,'F6','One-off waiver for OP [Refugee]','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','T','T','F',null,1,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (31,'F7','One-off waiver for IP [SWD476]','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','T','T','F',null,1,'T','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (32,'F8','One-off waiver for OP [SWD476]','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','T','T','F',null,1,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (33,'G1','One-off waiver for IP [OBS Package]','+N-OBSPKG-BO-,+N-OBSPKG-NBO-,','T','T','F',null,3,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (43,'N2','One-off waiver for PET services for OP','+N-OTHMED-PET-','T','T','F','F',1,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (44,'N1','One-off waiver for PET services for IP','+N-OTHMED-PET-','T','T','F','F',1,'T','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (45,'N3','One-off waiver for IP [Admin Charge]','+N-AC-1st,+N-AC-2nd,','T','F','F','F',null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (46,'N4','One-off waiver for OP [Admin Charge]','+N-AC-1st,+N-AC-2nd,','T','F','F','F',null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (49,'N7','Waiver for medical report to healthcare councils','+N-OTHMED-REP-4-M,+N-OTHMED-REP-6-M,+N-OTHMED-REP-7-M,+N-OTHMED-REP-8-M,+N-OTHMED-REP-1-M,+N-OTHMED-REP-2-M,+N-OTHMED-REP-3-M,+N-OTHMED-REP-9-M','T','T','F','F',1,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (50,'N8','Waiver for Blood Lead Level (BLL) Screening','+N-ATTD-BLL--M,+N-ATTD-BLLC--M,+N-ATTD-BLLP--M,+N-ATTD-BLLL--M,+N-ATTD-BLLO--M,+N-ATTD-BLLE--M,+N-ATTD-BLLS--M,+N-DRUG-SOPD-OP-M','T','T','F','F',1,'F','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (53,'S1','Special Waiver for in-patient services','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-DRUG-ANTIVIRAL','T','T','F','F',null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (54,'S2','Special Waiver for out-patient services','+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-DRUG-ANTIVIRAL','T','T','F','F',null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (47,'N5','One-off waiver for IP [Itemized Charge]','+I-PATH-,+I-RAD-,+I-DTP-,','T','F','F','F',null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (48,'N6','One-off waiver for OP [Itemized Charge]','+I-PATH-,+I-RAD-,+I-DTP-,','T','F','F','F',null,null,null);
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (51,'N9','Infectious diseases','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,+N-DRUG-SOPD-OP-M,+N-ATTD-,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-COMM-3--M,+N-DRUG-ANTIVIRAL','T','T','F','F',1,'T','T');
Insert into BILLING_SCHEMA.REF_WAIVER_TYPE (ID,NAME,DESCRIPTION,DEFAULT_FILTER,FIXED_AMT_ALLOWED_FLAG,ACTIVE_FLAG,APPLY_SAME_DAY_IP_ENCOUNTERS,INCLUDE_TAX,VALIDATE_MAX_DAYS,ALLOW_OVERRIDE_MAX_DAYS,REQUIRE_TARGET_ENTITY) values (52,'NO','(Please specify the reason in Note)','+N-WM-,+N-ADM---M,-N-WM-PWA-ACCOM-M,-N-WM-PWN-ACCOM-M,-N-WM-1st-ACCOM-M,-N-WM-2nd-ACCOM-M,-N-WM-PWNM--M,-N-WM-SAW--M,+N-DRUG-SOPD-OP-M,+N-ATTD-,+N-COMM-,+N-AC-1st,+N-AC-2nd,+I-PATH-,+I-RAD-,+I-DTP-,-N-ATTD-DHSP-ADIET,-N-ATTD-DHSP-EDIET,-N-ATTD-GDH-ADIET,-N-ATTD-GDH-EDIET,-N-ATTD-PDH-ADIET,-N-ATTD-PDH-EDIET,-N-ATTD-RDH-ADIET,-N-ATTD-RDH-EDIET,-N-WM-PWA-ADIET,-N-WM-PWA-EDIET,-N-WM-PW-DIET,-N-WM-PWN-ADIET,-N-WM-PWN-EDIET,+N-OTHMED-REP-6-M,+N-OTHMED-REP-8-M,+N-DRUG-ANTIVIRAL','T','T','F','F',1,'T','T');
