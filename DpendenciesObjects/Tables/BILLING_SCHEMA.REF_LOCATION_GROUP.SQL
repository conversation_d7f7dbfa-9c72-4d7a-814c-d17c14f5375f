--------------------------------------------------------
--  DDL for Table REF_LOCATION_GROUP
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_LOCATION_GROUP" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(20 CHAR), 
	"DESCRIPTION" VARCHAR2(100 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_LOCATION_GROUP"."NAME" IS 'The unique name of a particular reference table row.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_LOCATION_GROUP"."DESCRIPTION" IS 'The description of a particualr location reference type.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_LOCATION_GROUP"."ACTIVE_FLAG" IS 'Whether a particular reference table row is active (T = yes, F = no). Only active reference table values will appear in screen combo boxes.';
   COMMENT ON TABLE "BILLING_SCHEMA"."REF_LOCATION_GROUP"  IS 'Stores groupings available to locations.';
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PATIENT_ENQUIRY";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_LOCATION_GROUP
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_LOCATION_GROUP" ON "BILLING_SCHEMA"."REF_LOCATION_GROUP" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_LOCATION_GROUP
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_LOCATION_GROUP" BEFORE INSERT ON BILLING_SCHEMA.REF_LOCATION_GROUP FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_LOCATION_GROUP.NEXTVAL INTO :NEW.ID FROM DUAL; END; 



















/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_LOCATION_GROUP" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_LOCATION_GROUP
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION_GROUP" ADD CONSTRAINT "PK_REF_LOCATION_GROUP" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_LOCATION_GROUP"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION_GROUP" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION_GROUP" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_LOCATION_GROUP" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);


REM INSERTING into BILLING_SCHEMA.REF_LOCATION_GROUP
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (1,'2','SECOND CLASS','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (2,'1','FIRST CLASS','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (3,'3','THIRD CLASS','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (4,'B','SPECIAL ACCOMMODATION WARD','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (5,'T','Non-HA services (OLM-Private Ward (Hospital))?','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (6,'U','Non-HA services (OLM-Private Ward (Doctor))','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (7,'V','Non-HA services (OLM-Convalescent Ward)?','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (8,'W','Non-HA services (OLM-Day Surgery)?','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (9,'X','Infirmary ward to be charged at monthly rate','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (10,'Y','TBA','T');
Insert into BILLING_SCHEMA.REF_LOCATION_GROUP (ID,NAME,DESCRIPTION,ACTIVE_FLAG) values (11,'Z','TBA','T');
