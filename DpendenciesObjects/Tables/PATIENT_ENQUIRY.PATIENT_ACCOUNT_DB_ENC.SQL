--------------------------------------------------------
--  DDL for Table PATIENT_ACCOUNT_DB_ENC
--------------------------------------------------------

  CREATE TABLE "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" 
   (	"ENTITY_NUMBER" VARCHAR2(30 CHAR), 
	"HOSP_CODE" VARCHAR2(6 CHAR), 
	"ENCOUNTER_ID" NUMBER(30,0), 
	"ENCOUNTER_NUMBER" VARCHAR2(30 CHAR), 
	"START_DATE_TIME" TIMESTAMP (6), 
	"END_DATE_TIME" TIMESTAMP (6), 
	"CASE_TYPE" VARCHAR2(20 CHAR), 
	"STATUS_CODE" VARCHAR2(20 CHAR), 
	"LAST_PAY_CODE" VARCHAR2(20 CHAR), 
	"LAST_EIS_SPECIALTY" VARCHAR2(20 CHAR), 
	"LAST_WARD_CLASS" VARCHAR2(20 CHAR), 
	"LAST_WARD_CODE" VARCHAR2(25 CHAR), 
	"BILLED_UP_TO_DATE" TIMESTAMP (6), 
	"PMI_SOURCE_INDICATOR" VARCHAR2(20 CHAR), 
	"LAST_UPDATE_DTM" TIMESTAMP (6), 
	"PATIENT_ID" NUMBER(15,0), 
	"ENCOUNTER_TYPE_ID" NUMBER(9,0), 
	"LAST_ALLOCATION_ID" NUMBER(20,0), 
	"LAST_OPERATION_ID" NUMBER(20,0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

   COMMENT ON COLUMN "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC"."LAST_ALLOCATION_ID" IS 'The last id of BD_TRANSACTION which trans_type = ''AL''';
   COMMENT ON COLUMN "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC"."LAST_OPERATION_ID" IS 'The last id of BD_OPERATION';
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "ROSUP";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "HKHA";
  GRANT READ ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRC_RW_ROLE";
  GRANT READ ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRCPRDDBADM";
  GRANT INSERT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRCPRDDBADM";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRCPRDDBADM";
  GRANT DELETE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRCPRDDBBG";
  GRANT INSERT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRCPRDDBBG";
  GRANT SELECT ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PATIENT_ACCOUNT_DB_ENC_IDX2
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC_IDX2" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" ("ENCOUNTER_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index PATIENT_ACCOUNT_DB_ENC_IDX1
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC_IDX1" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_ENC" ("ENTITY_NUMBER", "START_DATE_TIME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
