--------------------------------------------------------
--  DDL for Table REF_SPECIALTY
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."REF_SPECIALTY" 
   (	"ID" NUMBER(9,0), 
	"NAME" VARCHAR2(20 CHAR), 
	"DESCRIPTION" VARCHAR2(100 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR), 
	"TYPE" VARCHAR2(30 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_SPECIALTY"."NAME" IS 'The unique name of a particular reference table row.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_SPECIALTY"."DESCRIPTION" IS 'The description of a type of provider specialty type.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."REF_SPECIALTY"."ACTIVE_FLAG" IS 'Whether a particular reference table row is active (T = yes, F = no). Only active reference table values will appear in screen combo boxes.';
   COMMENT ON TABLE "BILLING_SCHEMA"."REF_SPECIALTY"  IS 'Stores speciality area of the provider.';
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "ECHARGE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCNBI";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCNBI";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCNBI";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PATIENT_ENQUIRY";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_ADMIN_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_ADMIN_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_ADMIN_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SUPPORT_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT READ ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_OPER_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HPSSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCSUP";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "INC054";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "CYT103";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "CHT132";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HPS";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "KCK444";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "CHANCHJ";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "WONGYE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SA_ROLE";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SA_ROLE";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SA_ROLE";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SA_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SA_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SA_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SA_ROLE";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_SA_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "RO_ROLE";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_RO_ROLE";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_RW_ROLE";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_RW_ROLE";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_RW_ROLE";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRC_RW_ROLE";
  GRANT READ ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCPRDDBSUP";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCPRDDBADM";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCPRDDBADM";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCPRDDBADM";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCPRDDBADM";
  GRANT DELETE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCPRDDBBG";
  GRANT INSERT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCPRDDBBG";
  GRANT SELECT ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCPRDDBBG";
  GRANT UPDATE ON "BILLING_SCHEMA"."REF_SPECIALTY" TO "PBRCPRDDBBG";
--------------------------------------------------------
--  DDL for Index PK_REF_SPECIALTY
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_REF_SPECIALTY" ON "BILLING_SCHEMA"."REF_SPECIALTY" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_REF_SPECIALTY
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_REF_SPECIALTY" BEFORE INSERT ON BILLING_SCHEMA.REF_SPECIALTY FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_REF_SPECIALTY.NEXTVAL INTO :NEW.ID FROM DUAL; END; 



















/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_REF_SPECIALTY" ENABLE;
--------------------------------------------------------
--  Constraints for Table REF_SPECIALTY
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."REF_SPECIALTY" ADD CONSTRAINT "PK_REF_SPECIALTY" PRIMARY KEY ("ID")
  USING INDEX "BILLING_SCHEMA"."PK_REF_SPECIALTY"  ENABLE;
  ALTER TABLE "BILLING_SCHEMA"."REF_SPECIALTY" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_SPECIALTY" MODIFY ("NAME" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."REF_SPECIALTY" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);

REM INSERTING into BILLING_SCHEMA.REF_SPECIALTY
SET DEFINE OFF;
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9306,'QMH-SMHC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9307,'QMH-SMSC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9308,'QMH-SMSS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9309,'QMH-SPTH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9310,'QMH-WDTC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9311,'SJH-GONL','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9312,'SJH-GOPG','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9313,'SJH-GOSJ','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9314,'SJH-GOSO','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9315,'SJH-ORTH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9316,'SJH-PHYA','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9317,'TKO-DIET','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9318,'TKO-FMGI','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9319,'TKO-HTNC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9320,'TKO-PHYA','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9321,'TKO-PHYC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9322,'TKO-PHYP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9323,'TKO-SPTH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9324,'TKO-TKG1','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9325,'TKO-TKG2','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9326,'TKO-TKG3','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9327,'TMH-ADOL','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9328,'TMH-CCDC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9329,'TMH-CCDS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9330,'TMH-CCNC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9331,'TMH-CCNS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9332,'TMH-CGAS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9333,'TMH-CPCP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9334,'TMH-DHSP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9335,'TMH-DIET','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9336,'TMH-FMTC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9337,'TMH-FNAC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9338,'TMH-GERN','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9339,'TMH-GOKA','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9340,'TMH-GOTM','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9341,'TMH-GOTN','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9342,'TMH-GOTS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9343,'TMH-GOWH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9344,'TMH-GOYF','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9345,'TMH-GOYL','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9346,'TMH-GOYO','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9347,'TMH-HANA','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9348,'TMH-HASC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9349,'TMH-HCTS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9350,'TMH-HENT','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9351,'TMH-HEON','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9352,'TMH-HGYN','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9353,'TMH-HMED','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9354,'TMH-HORT','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9355,'TMH-HPAE','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9356,'TMH-HPDS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9357,'TMH-HPOS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9358,'TMH-HPSY','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9359,'TMH-HREN','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9360,'TMH-HSUR','high dependency surgery','T','IPAS Specialty');
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9361,'TMH-HURO','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9362,'TMH-HVAS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9363,'TMH-MCAR','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9364,'TMH-MEND','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9365,'TMH-MHEM','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9366,'TMH-MIDS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9367,'TMH-MPSU','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9368,'TMH-MREH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9369,'TMH-MREN','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9370,'TMH-MRHE','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9371,'TMH-NTWC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9372,'TMH-OCCC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9373,'TMH-OCMG','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9374,'TMH-OMPT','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9375,'TMH-OMSD','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9376,'TMH-PADS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9377,'TMH-PBIP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9378,'TMH-PBOP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9379,'TMH-PCOM','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9380,'TMH-PHYA','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9381,'TMH-PHYC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9382,'TMH-PHYP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9383,'TMH-PP01','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9384,'TMH-PP02','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9385,'TMH-PP03','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9386,'TMH-PP04','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9387,'TMH-PP05','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9388,'TMH-PP06','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9389,'TMH-PSYT','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9390,'TMH-SCCC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9391,'TMH-SMHC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9392,'TMH-SPTH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9393,'TMH-T6BP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9394,'TMH-T6EC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9395,'TMH-T6EE','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9396,'TMH-T6EP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9397,'TMH-T6EU','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9398,'TMH-T6HO','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9399,'TMH-T6KH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9400,'TMH-T6MO','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9401,'TMH-T6NC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9402,'TMH-T6OG','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9403,'TMH-T6PF','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9404,'TMH-T6TR','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9405,'TMH-TMCC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9406,'TMH-TMTC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9407,'TMH-VOIC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9408,'TMH-YLCC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9409,'TMH-YLTC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9410,'TPH-MSSC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9411,'TPH-MSSI','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9412,'TWE-DIET','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9413,'TWE-GOTE','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9414,'TWE-ICRC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9415,'TWE-MSSI','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9416,'TWE-ORTK','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9417,'TWE-PHYA','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9418,'TWE-SPTH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9419,'TWH-DIET','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9420,'TWH-GOPD','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9421,'TWH-MSSI','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9422,'TWH-SKIN','Dermatology - Laser Service','T','IPAS Specialty');
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9423,'UCH-COCC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9424,'UCH-DIET','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9425,'UCH-GKBH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9426,'UCH-GKTJ','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9427,'UCH-GLTP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9428,'UCH-GNTK','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9429,'UCH-GSLG','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9430,'UCH-HASC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9431,'UCH-MSSC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9432,'UCH-MSSI','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9433,'UCH-MSSP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9434,'UCH-PHYA','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9435,'UCH-PHYC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9436,'UCH-PHYP','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9437,'UCH-PSAD','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9438,'UCH-SMSY','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9439,'UCH-SPTH','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9440,'WCH-CGAT','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9441,'WTS-MSSC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9442,'WTS-MSSI','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9443,'YCH-CGAS','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9444,'YCH-DIET','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9445,'YCH-GLTC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9446,'YCH-GMWY','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9447,'YCH-MSSC','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9448,'YCH-MSSI','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9449,'YCH-OCCT','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9450,'YCH-PHYA','no description provided','T',null);
Insert into BILLING_SCHEMA.REF_SPECIALTY (ID,NAME,DESCRIPTION,ACTIVE_FLAG,TYPE) values (9451,'YCH-SPTH','no description provided','T',null);

