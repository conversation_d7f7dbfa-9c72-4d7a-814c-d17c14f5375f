--------------------------------------------------------
--  DDL for Table AS_ENCOUNTER_FIN_CLASS_HISTORY
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" 
   (	"ID" NUMBER(15,0), 
	"ENCOUNTER_ID" NUMBER(15,0), 
	"FIN_CLASS_ID" NUMBER(9,0), 
	"START_DATE_TIME" TIMESTAMP (6), 
	"CREATED_USER_ID" NUMBER(9,0), 
	"MODIFIED_USER_ID" NUMBER(9,0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY"."ENCOUNTER_ID" IS 'Represents a FK link to a particular encounter.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY"."FIN_CLASS_ID" IS 'The financial class of an encounter. Multiple rows represent the financial class history for an encounter .';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY"."START_DATE_TIME" IS 'The effective start date time of a encounter financial class. The entries with different start dates form the financial class history for an encounter.';
   COMMENT ON TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY"  IS 'Stores financial class history data for encounters';
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCSVC";
  GRANT ALTER ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index FK_ENC_FIN_CLASS
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENC_FIN_CLASS" ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" ("FIN_CLASS_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_ENC_FIN_CLASS_HIST
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_ENC_FIN_CLASS_HIST" ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index UNIQUE_AS_ENC_FIN_CLASS_HIST
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."UNIQUE_AS_ENC_FIN_CLASS_HIST" ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" ("ENCOUNTER_ID", "START_DATE_TIME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FIN_CLASS_HISTORY_INDEX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FIN_CLASS_HISTORY_INDEX" ON "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" ("ENCOUNTER_ID", "FIN_CLASS_ID", "START_DATE_TIME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_AS_ENC_FIN_CLASS_HIST_UPD
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_AS_ENC_FIN_CLASS_HIST_UPD" BEFORE UPDATE ON BILLING_SCHEMA.AS_ENCOUNTER_FIN_CLASS_HISTORY 
FOR EACH ROW 
DECLARE
MAX_SERVICE_DATE timestamp(6);
MAX_CHARGE_DATE timestamp(6);
BEGIN 
--Simon070818: OALA medium solution
IF :NEW.FIN_CLASS_ID = 61 -- 'TPA' --CHECKED: same ID was used in SIT and production
--or :NEW.FIN_CLASS_ID = 43
THEN
  --Simon20220208:start: PIHAS-543: schema change by PHS
  update billing_schema.cssa_waiver_declared d
  set CREATED_DATE = SYSTIMESTAMP
  , ENCOUNTER_NUMBER =  (select ENCOUNTER_NUMBER 
                        FROM BILLING_SCHEMA.AS_ENCOUNTER
                        WHERE ID = :NEW.ENCOUNTER_ID
                        )
  where exists (select 1 from billing_schema.as_encounter enc, BILLING_SCHEMA.AS_ENTITY_DETAIL ED where enc.id = :NEW.ENCOUNTER_ID and ENC.RECIPIENT_ENTITY_DETAIL_ID = ED.ID
  and d.hkid = ED.ENTITY_NUMBER)
  ;
  --Simon20220208:end: PIHAS-543: schema change by PHS

  INSERT INTO billing_schema.cssa_waiver_declared
  --SELECT ED.ENTITY_NUMBER, SYSTIMESTAMP, ORG.CODE, ENC.ENCOUNTER_NUMBER --Simon20220208: PIHAS-543: schema change by PHS
  SELECT SEQ_CSSA_WAIVER_DECLARED.nextval, ED.ENTITY_NUMBER, SYSTIMESTAMP, ORG.CODE, ENC.ENCOUNTER_NUMBER --Simon20220208: PIHAS-543: schema change by PHS
  FROM BILLING_SCHEMA.AS_ENCOUNTER ENC, BILLING_SCHEMA.AS_ENTITY_DETAIL ED, BILLING_SCHEMA.OS_ORGANISATION ORG
  WHERE ENC.ID = :NEW.ENCOUNTER_ID
  AND ENC.RECIPIENT_ENTITY_DETAIL_ID = ED.ID
  AND ORG.ID = ENC.ORGANISATIONAL_ID
  and not exists (select 1 from billing_schema.cssa_waiver_declared where hkid = ED.ENTITY_NUMBER)
  ;

  SELECT nvl(max(start_date_time),to_date('19000101','yyyymmdd')) into MAX_SERVICE_DATE from billing_schema.as_service where encounter_id = :NEW.ENCOUNTER_ID;
  SELECT nvl(max(service_start_date_time),to_date('19000101','yyyymmdd')) into MAX_CHARGE_DATE from billing_schema.bd_charge where encounter_id = :NEW.ENCOUNTER_ID;

  INSERT INTO billing_schema.cssa_waiver_check_txn (hkid, create_datetime, hospital, encounter_number, check_from_date, check_to_date, type, priority, process_status , process_datetime, hkid_ind, dob, exact_dob, nep_search_key, process_queue_id)
  --SELECT case when ed.entity_number like 'U%' and length(trim(ed.entity_number)) <> 8 then ed.entity_number else ' ' || ed.entity_number end hkid --SF20171211: bug fix hkid space
  SELECT case when substr(ed.entity_number,2,1) not in ('0','1','2','3','4','5','6','7','8','9') then ed.entity_number else ' ' || ed.entity_number end hkid --SF20171211: bug fix hkid space
  , SYSTIMESTAMP, ORG.CODE, ENC.ENCOUNTER_NUMBER, ENC.START_DATE_TIME
  , case when ENC.END_DATE_TIME is null then sysdate
        when ENC.END_DATE_TIME > MAX_SERVICE_DATE and ENC.END_DATE_TIME > MAX_CHARGE_DATE then ENC.END_DATE_TIME 
        when MAX_CHARGE_DATE > MAX_SERVICE_DATE then MAX_CHARGE_DATE 
        else MAX_SERVICE_DATE end
  , 'Online', 1, 'I', null, case when ed.entity_number like 'U%' and length(trim(ed.entity_number)) <> 8 then 'N' else 'Y' end
  , nvl(r.DATE_OF_BIRTH, to_date('19000101','yyyymmdd')), case when r.IS_EXACT_DATE_OF_BIRTH = 'T' then 'Y' else 'N' end
  , ( select ID_NUMBER from billing_schema.as_recipient_identifier  where RECIPIENT_ENTITY_DETAIL_ID = ed.id and type_id = 28 ) 
  ,(select mod(ed.id,value) + 1 from BILLING_SCHEMA.central_waiver_config where key = 'ONLINE_QUEUE_NO') 
  FROM BILLING_SCHEMA.AS_ENCOUNTER ENC, BILLING_SCHEMA.AS_ENTITY_DETAIL ED, BILLING_SCHEMA.OS_ORGANISATION ORG, BILLING_SCHEMA.AS_RECIPIENT r
  WHERE ENC.ID = :NEW.ENCOUNTER_ID
  AND ENC.RECIPIENT_ENTITY_DETAIL_ID = ED.ID
  AND ORG.ID = ENC.ORGANISATIONAL_ID
  and r.ENTITY_DETAIL_ID = ed.id
  ;

  --Encounter wf script also has this banner seting 
  update billing_schema.as_encounter enc
  set banner_message = 'Waiver checking is in progress, please refresh to check the waiver result in Action Reason'
  --,APPROVAL_STATUS_NOTES = nvl(APPROVAL_STATUS_NOTES,' ') || '*OALA waiver checking started at ' || to_char(systimestamp,'yyyymmdd hh24:mi:ss') ||'***'
--  ,APPROVAL_STATUS_NOTES = case when  ( select ID_NUMBER from billing_schema.as_recipient_identifier  where RECIPIENT_ENTITY_DETAIL_ID = enc.RECIPIENT_ENTITY_DETAIL_ID and type_id = 28 ) is not null then APPROVAL_STATUS_NOTES 
--                            else  nvl(APPROVAL_STATUS_NOTES,' ') || '*OALA waiver result: Missing patient idendifer "NEP", please input and retry' end
  where id = :NEW.ENCOUNTER_ID
  --only display if age is >= 75
  and exists (select 1 from BILLING_SCHEMA.AS_RECIPIENT r 
                        where ENC.RECIPIENT_ENTITY_DETAIL_ID = r.ENTITY_DETAIL_ID
                        and (SUBSTR(TO_CHAR(sysdate, 'YYYYMMDD'),1,4) - SUBSTR(TO_CHAR(DATE_OF_BIRTH,'YYYYMMDD'),1,4))
                                -
                                (CASE WHEN SUBSTR(TO_CHAR(sysdate, 'YYYYMMDD'),5,2) < SUBSTR(TO_CHAR(DATE_OF_BIRTH,'YYYYMMDD'),5,2) THEN 1
                                WHEN SUBSTR(TO_CHAR(sysdate, 'YYYYMMDD'),5,2) = SUBSTR(TO_CHAR(DATE_OF_BIRTH,'YYYYMMDD'),5,2) AND
                                SUBSTR(TO_CHAR(sysdate, 'YYYYMMDD'),7,2) < SUBSTR(TO_CHAR(DATE_OF_BIRTH,'YYYYMMDD'),7,2) THEN 1
                                ELSE 0
                                END) >= 70 --AGE --AGE **** note ****temperary comment as all the test case in SIT are age 72
  )
  ;
  
END IF;
--SF070818: OALA medium solution
END;


/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_AS_ENC_FIN_CLASS_HIST_UPD" ENABLE;
--------------------------------------------------------
--  DDL for Trigger TRIG_AS_ENC_FIN_CLASS_HISTORY
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_AS_ENC_FIN_CLASS_HISTORY" BEFORE INSERT ON BILLING_SCHEMA.AS_ENCOUNTER_FIN_CLASS_HISTORY 
FOR EACH ROW 
DECLARE
MAX_SERVICE_DATE timestamp(6);
MAX_CHARGE_DATE timestamp(6);
BEGIN 
SELECT BILLING_SCHEMA.SEQ_AS_ENC_FIN_CLASS_HISTORY.NEXTVAL INTO :NEW.ID FROM DUAL; 
--Simon070818: OALA medium solution

IF :NEW.FIN_CLASS_ID = 61 -- 'TPA' --CHECKED: same ID was used in SIT and production
THEN
  --Simon20220208:start: PIHAS-543: schema change by PHS
  update billing_schema.cssa_waiver_declared d
  set CREATED_DATE = SYSTIMESTAMP
  , ENCOUNTER_NUMBER =  (select ENCOUNTER_NUMBER 
                        FROM BILLING_SCHEMA.AS_ENCOUNTER
                        WHERE ID = :NEW.ENCOUNTER_ID
                        )
  where exists (select 1 from billing_schema.as_encounter enc, BILLING_SCHEMA.AS_ENTITY_DETAIL ED where enc.id = :NEW.ENCOUNTER_ID and ENC.RECIPIENT_ENTITY_DETAIL_ID = ED.ID
  and d.hkid = ED.ENTITY_NUMBER)
  ;
  --Simon20220208:end: PIHAS-543: schema change by PHS
  INSERT INTO billing_schema.cssa_waiver_declared
  --SELECT ED.ENTITY_NUMBER, SYSTIMESTAMP, ORG.CODE, ENC.ENCOUNTER_NUMBER --Simon20220208: PIHAS-543: schema change by PHS
  SELECT SEQ_CSSA_WAIVER_DECLARED.nextval, ED.ENTITY_NUMBER, SYSTIMESTAMP, ORG.CODE, ENC.ENCOUNTER_NUMBER --Simon20220208: PIHAS-543: schema change by PHS
  FROM BILLING_SCHEMA.AS_ENCOUNTER ENC, BILLING_SCHEMA.AS_ENTITY_DETAIL ED, BILLING_SCHEMA.OS_ORGANISATION ORG
  WHERE ENC.ID = :NEW.ENCOUNTER_ID
  AND ENC.RECIPIENT_ENTITY_DETAIL_ID = ED.ID
  AND ORG.ID = ENC.ORGANISATIONAL_ID
  and not exists (select 1 from billing_schema.cssa_waiver_declared where hkid = ED.ENTITY_NUMBER)
  ;

  SELECT nvl(max(start_date_time),to_date('19000101','yyyymmdd')) into MAX_SERVICE_DATE from billing_schema.as_service where encounter_id = :NEW.ENCOUNTER_ID;
  SELECT nvl(max(service_start_date_time),to_date('19000101','yyyymmdd')) into MAX_CHARGE_DATE from billing_schema.bd_charge where encounter_id = :NEW.ENCOUNTER_ID;

  INSERT INTO billing_schema.cssa_waiver_check_txn (hkid, create_datetime, hospital, encounter_number, check_from_date, check_to_date, type, priority, process_status , process_datetime, hkid_ind, dob, exact_dob, nep_search_key,process_queue_id)
  --SELECT case when ed.entity_number like 'U%' and length(trim(ed.entity_number)) <> 8 then ed.entity_number else ' ' || ed.entity_number end hkid --SF20171211: bug fix hkid space
  SELECT case when substr(ed.entity_number,2,1) not in ('0','1','2','3','4','5','6','7','8','9') then ed.entity_number else ' ' || ed.entity_number end hkid --SF20171211: bug fix hkid space
  , SYSTIMESTAMP, ORG.CODE, ENC.ENCOUNTER_NUMBER, ENC.START_DATE_TIME
  , case when ENC.END_DATE_TIME is null then sysdate 
        when ENC.END_DATE_TIME > MAX_SERVICE_DATE and ENC.END_DATE_TIME > MAX_CHARGE_DATE then ENC.END_DATE_TIME 
        when MAX_CHARGE_DATE > MAX_SERVICE_DATE then MAX_CHARGE_DATE 
        else MAX_SERVICE_DATE end
  , 'Online', 1, 'I', null, case when ed.entity_number like 'U%' and length(trim(ed.entity_number)) <> 8 then 'N' else 'Y' end
  , nvl(r.DATE_OF_BIRTH, to_date('19000101','yyyymmdd')), case when r.IS_EXACT_DATE_OF_BIRTH = 'T' then 'Y' else 'N' end
  , ( select ID_NUMBER from billing_schema.as_recipient_identifier  where RECIPIENT_ENTITY_DETAIL_ID = ed.id and type_id = 28 ) NEP_SEARCH_KEY
  ,(select mod(ed.id,value) + 1 from BILLING_SCHEMA.central_waiver_config where key = 'ONLINE_QUEUE_NO') 
  FROM BILLING_SCHEMA.AS_ENCOUNTER ENC, BILLING_SCHEMA.AS_ENTITY_DETAIL ED, BILLING_SCHEMA.OS_ORGANISATION ORG, BILLING_SCHEMA.AS_RECIPIENT r
  WHERE ENC.ID = :NEW.ENCOUNTER_ID
  AND ENC.RECIPIENT_ENTITY_DETAIL_ID = ED.ID
  AND ORG.ID = ENC.ORGANISATIONAL_ID
  and r.ENTITY_DETAIL_ID = ed.id
  ;
  --Encounter wf script also has this banner setting 
  update billing_schema.as_encounter enc
  set banner_message = 'Waiver checking is in progress, please refresh to check the waiver result in Action Reason'
  --,APPROVAL_STATUS_NOTES = nvl(APPROVAL_STATUS_NOTES,' ') || '*OALA waiver checking started at ' || to_char(systimestamp,'yyyymmdd hh24:mi:ss') ||'***'
--  ,APPROVAL_STATUS_NOTES = case when  ( select ID_NUMBER from billing_schema.as_recipient_identifier  where RECIPIENT_ENTITY_DETAIL_ID = enc.RECIPIENT_ENTITY_DETAIL_ID and type_id = 28 ) is not null then APPROVAL_STATUS_NOTES 
--                            else  nvl(APPROVAL_STATUS_NOTES,' ') || '*OALA waiver result: Missing patient idendifer "NEP", please input and retry' end
  where id = :NEW.ENCOUNTER_ID
  --only display if age is >= 75
  and exists (select 1 from BILLING_SCHEMA.AS_RECIPIENT r 
                        where ENC.RECIPIENT_ENTITY_DETAIL_ID = r.ENTITY_DETAIL_ID
                        and (SUBSTR(TO_CHAR(sysdate, 'YYYYMMDD'),1,4) - SUBSTR(TO_CHAR(DATE_OF_BIRTH,'YYYYMMDD'),1,4))
                                -
                                (CASE WHEN SUBSTR(TO_CHAR(sysdate, 'YYYYMMDD'),5,2) < SUBSTR(TO_CHAR(DATE_OF_BIRTH,'YYYYMMDD'),5,2) THEN 1
                                WHEN SUBSTR(TO_CHAR(sysdate, 'YYYYMMDD'),5,2) = SUBSTR(TO_CHAR(DATE_OF_BIRTH,'YYYYMMDD'),5,2) AND
                                SUBSTR(TO_CHAR(sysdate, 'YYYYMMDD'),7,2) < SUBSTR(TO_CHAR(DATE_OF_BIRTH,'YYYYMMDD'),7,2) THEN 1
                                ELSE 0
                                END) >= 70 --AGE **** note ****temperary comment as all the test case in SIT are age 72
  )
  ;
END IF;
--SF070818: OALA medium solution
END;


/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_AS_ENC_FIN_CLASS_HISTORY" ENABLE;
--------------------------------------------------------
--  DDL for Trigger TRIG_FIN_CLASS_HISTORY_I
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_FIN_CLASS_HISTORY_I" 
    AFTER DELETE OR INSERT OR UPDATE
    ON BILLING_SCHEMA.AS_ENCOUNTER_FIN_CLASS_HISTORY
    FOR EACH ROW
    DECLARE

    BEGIN
        INSERT INTO BILLING_SCHEMA.PADB_ACTIVITY_LOG
        ( ENCOUNTER_ID, LOG_SOURCE, LOG_DATE_TIME, LOG_PROCESS_STATUS )
        VALUES (
            :NEW.ENCOUNTER_ID, 'AS_ENCOUNTER_FIN_CLASS_HISTORY', SYSTIMESTAMP, 'I'
        );
    END;

/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_FIN_CLASS_HISTORY_I" ENABLE;
--------------------------------------------------------
--  Constraints for Table AS_ENCOUNTER_FIN_CLASS_HISTORY
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" MODIFY ("ENCOUNTER_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" MODIFY ("FIN_CLASS_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_FIN_CLASS_HISTORY" MODIFY ("START_DATE_TIME" NOT NULL ENABLE);
