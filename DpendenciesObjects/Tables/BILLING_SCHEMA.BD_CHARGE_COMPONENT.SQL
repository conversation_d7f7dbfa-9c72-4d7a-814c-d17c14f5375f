--------------------------------------------------------
--  DDL for Table BD_CHARGE_COMPONENT
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" 
   (	"ID" NUMBER(15,0), 
	"CHARGE_ID" NUMBER(15,0), 
	"PAYOR_ENTITY_DETAIL_ID" NUMBER(15,0), 
	"INVOICE_ID" NUMBER(15,0), 
	"INVOICE_LINE_NUMBER" NUMBER(9,0), 
	"IGNORE_DETERMINATION_FLAG" VARCHAR2(1 CHAR), 
	"ELECTRONIC_STATUS" VARCHAR2(1 BYTE), 
	"ELECTRONIC_SUBMISSION_DTE_TME" TIMESTAMP (6), 
	"ELECTRONIC_REJECTION_REASON_ID" NUMBER(9,0), 
	"ELECTRONIC_SUBMISSION_ID" VARCHAR2(50 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE_COMPONENT"."CHARGE_ID" IS 'The parent charge of a particular charge component.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE_COMPONENT"."PAYOR_ENTITY_DETAIL_ID" IS 'The entity that is the designated payor for a charge component. .';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE_COMPONENT"."INVOICE_ID" IS 'A unique internal identifier for an invoice which is assigned by PBRC.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_CHARGE_COMPONENT"."IGNORE_DETERMINATION_FLAG" IS 'Whether a charge component is excluded from charge determination';
   COMMENT ON TABLE "BILLING_SCHEMA"."BD_CHARGE_COMPONENT"  IS 'Stores the charge components which make up a charge.';
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCSVC";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index FK_PAYOR_CHARGE_COMPONENT
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_PAYOR_CHARGE_COMPONENT" ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" ("PAYOR_ENTITY_DETAIL_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_CHARGE_COMP_INVOICE
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_CHARGE_COMP_INVOICE" ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" ("INVOICE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_CHARGE_CHARGE_COMPONENT
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_CHARGE_CHARGE_COMPONENT" ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" ("CHARGE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_BD_CHARGE_COMPONENT
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_BD_CHARGE_COMPONENT" ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_CHARGE_COMPONENT_N
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_CHARGE_COMPONENT_N" ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" ("PAYOR_ENTITY_DETAIL_ID", "ID", "INVOICE_ID", "CHARGE_ID", "INVOICE_LINE_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_CHARGE_COMPONENT_N99
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_CHARGE_COMPONENT_N99" ON "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" ("ID", "PAYOR_ENTITY_DETAIL_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  Constraints for Table BD_CHARGE_COMPONENT
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" MODIFY ("CHARGE_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_CHARGE_COMPONENT" MODIFY ("PAYOR_ENTITY_DETAIL_ID" NOT NULL ENABLE);
