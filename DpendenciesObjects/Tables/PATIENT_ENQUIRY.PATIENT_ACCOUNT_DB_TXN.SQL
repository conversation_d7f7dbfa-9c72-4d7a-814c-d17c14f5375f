--------------------------------------------------------
--  DDL for Table PATIENT_ACCOUNT_DB_TXN
--------------------------------------------------------

  CREATE TABLE "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_TXN" 
   (	"PAYOR_ENTITY_NUMBER" VARCHAR2(50 CHAR), 
	"PAYOR_ENTITY_ID" NUMBER(30,0), 
	"PAYOR_PI_KEY" VARCHAR2(20 CHAR), 
	"TXN_ID" NUMBER(30,0), 
	"TRANSACTION_NUMBER" VARCHAR2(50 CHAR), 
	"TRANSACTION_HOSP" VARCHAR2(5 CHAR), 
	"TRANSACTION_SOURCE" VARCHAR2(10 CHAR), 
	"DATE_TIME_ENTERED" TIMESTAMP (6), 
	"DATE_TIME_ENACTED" TIMESTAMP (6), 
	"TRANS_TYPE" VARCHAR2(5 CHAR), 
	"TRANS_SUBTYPE" VARCHAR2(100 CHAR), 
	"TRANS_REASON_CODE" VARCHAR2(20 CHAR), 
	"TRANS_CHARGE_ALLOC_CODE" VARCHAR2(100 CHAR), 
	"PAYMENT_METHOD" VARCHAR2(100 CHAR), 
	"TRANSACTION_TOTAL_AMOUNT" NUMBER(20,2), 
	"REMAING_OR_ALLOCATED_AMOUNT" NUMBER(20,2), 
	"LAST_ALLOC_DATETIME" TIMESTAMP (6), 
	"ENCOUNTER_NUMBER" VARCHAR2(50 CHAR), 
	"ENCOUNTER_HOSP" VARCHAR2(5 CHAR), 
	"CHARGE_ID" NUMBER(20,0), 
	"SERVICE_CODE" VARCHAR2(50 CHAR), 
	"SERVICE_START_DATE_TIME" TIMESTAMP (6), 
	"SERVICE_DESC_PBRC" VARCHAR2(200 CHAR), 
	"SERVICE_DESC_ENG" VARCHAR2(200 CHAR), 
	"SERVICE_DESC_CHI" VARCHAR2(200 CHAR), 
	"INVOICE_NUMBER" VARCHAR2(20 CHAR), 
	"INVOICE_ID" NUMBER(30,0), 
	"INVOICE_HOSP" VARCHAR2(5 CHAR), 
	"WAIVER_SNAPSHOT_ID" NUMBER(20,0), 
	"WAIVER_REF_NUMBER" VARCHAR2(20 CHAR), 
	"WAIVER_TYPE" VARCHAR2(15 CHAR), 
	"WAIVER_SOURCE" VARCHAR2(15 CHAR), 
	"GL_PAY_CODE" VARCHAR2(50 CHAR), 
	"GL_CASE_TYPE" VARCHAR2(50 CHAR), 
	"GL_EIS_SERVICE_TYPE" VARCHAR2(50 CHAR), 
	"GL_EIS_SPECIALTY" VARCHAR2(50 CHAR), 
	"GL_EIS_SUB_SPECIALTY" VARCHAR2(50 CHAR), 
	"GL_OPAS_SHROFF_GROUP" VARCHAR2(50 CHAR), 
	"GL_OPAS_SPECIALTY" VARCHAR2(50 CHAR), 
	"GL_MISC" VARCHAR2(50 CHAR), 
	"GL_ENCOUNTER_MISC" VARCHAR2(50 CHAR), 
	"GL_SERVICE_MISC" VARCHAR2(50 CHAR), 
	"GL_WARD_CLASS" VARCHAR2(50 CHAR), 
	"GL_WARD_CODE" VARCHAR2(50 CHAR), 
	"LAST_UPDATE_TIME" TIMESTAMP (6)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index IDX_PATIENT_ACCOUNT_DB_TXN
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."IDX_PATIENT_ACCOUNT_DB_TXN" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_TXN" ("TXN_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index IDX_PATIENT_ACCOUNT_DB_TXN_2
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."IDX_PATIENT_ACCOUNT_DB_TXN_2" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_TXN" ("TRANSACTION_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index IDX_PATIENT_ACCOUNT_DB_TXN_3
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."IDX_PATIENT_ACCOUNT_DB_TXN_3" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_TXN" ("SERVICE_START_DATE_TIME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index IDX_PATIENT_ACCOUNT_DB_TXN_4
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."IDX_PATIENT_ACCOUNT_DB_TXN_4" ON "PATIENT_ENQUIRY"."PATIENT_ACCOUNT_DB_TXN" ("PAYOR_ENTITY_NUMBER", "TRANS_TYPE") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
