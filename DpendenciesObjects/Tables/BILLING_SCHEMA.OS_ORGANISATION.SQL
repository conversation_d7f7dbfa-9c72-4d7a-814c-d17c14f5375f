--------------------------------------------------------
--  DDL for Table OS_ORGANISATION
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."OS_ORGANISATION" 
   (	"ID" NUMBER(9,0), 
	"CODE" VARCHAR2(6 CHAR), 
	"NAME" VARCHAR2(50 CHAR), 
	"LOCATION_ADDRESS_LINE_1" VARCHAR2(100 CHAR), 
	"LOCATION_ADDRESS_LINE_2" VARCHAR2(100 CHAR), 
	"LOCATION_SUBURB" VARCHAR2(200 CHAR), 
	"LOCATION_POSTCODE" VARCHAR2(6 CHAR), 
	"LOCATION_STATE" VARCHAR2(50 CHAR), 
	"LOCATION_COUNTRY" VARCHAR2(50 CHAR), 
	"LOCATION_PHONE" VARCHAR2(30 CHAR), 
	"LOCATION_FAX" VARCHAR2(30 CHAR), 
	"POSTAL_ADDRESS_LINE_1" VARCHAR2(100 CHAR), 
	"POSTAL_ADDRESS_LINE_2" VARCHAR2(100 CHAR), 
	"POSTAL_SUBURB" VARCHAR2(200 CHAR), 
	"POSTAL_POSTCODE" VARCHAR2(6 CHAR), 
	"POSTAL_STATE" VARCHAR2(50 CHAR), 
	"POSTAL_COUNTRY" VARCHAR2(50 CHAR), 
	"PARENT_ORGANISATION_ID" NUMBER(9,0), 
	"USE_PARENT_LOCATION" VARCHAR2(1 CHAR), 
	"USE_PARENT_POSTAL" VARCHAR2(1 CHAR), 
	"USE_PARENT_PHONE" VARCHAR2(1 CHAR), 
	"ACTIVE_FLAG" VARCHAR2(1 CHAR), 
	"INSTITUTION_CODE" NUMBER(3,0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."CODE" IS 'A unique identifier for a particular organisation in the PBRC organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."NAME" IS 'The name given to a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."LOCATION_ADDRESS_LINE_1" IS 'Part 1 of an organisation''s location address.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."LOCATION_ADDRESS_LINE_2" IS 'Part 2 of an organisation''s location address.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."LOCATION_SUBURB" IS 'The location suburb of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."LOCATION_POSTCODE" IS 'The location post code of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."LOCATION_STATE" IS 'The location state of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."LOCATION_COUNTRY" IS 'The country of an organisation''s location address .';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."LOCATION_PHONE" IS 'The location phone number of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."LOCATION_FAX" IS 'The FAX of an organisation''s location address .';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."POSTAL_ADDRESS_LINE_1" IS 'Line 1 of the postal address of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."POSTAL_ADDRESS_LINE_2" IS 'Line 2 of the postal address of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."POSTAL_SUBURB" IS 'The suburb of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."POSTAL_POSTCODE" IS 'The post code of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."POSTAL_STATE" IS 'The state of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."POSTAL_COUNTRY" IS 'The country of a particular organisation in the organisational structure.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."PARENT_ORGANISATION_ID" IS 'Used to link a particular organisation to another organisation. These links form the organisational structure of the application.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."USE_PARENT_LOCATION" IS 'Use location details from the parent of this organisation.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."USE_PARENT_POSTAL" IS 'Use postal details from the parent of this organisation.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."OS_ORGANISATION"."USE_PARENT_PHONE" IS 'Use phone details from the parent of this organisation.';
   COMMENT ON TABLE "BILLING_SCHEMA"."OS_ORGANISATION"  IS 'Stores organizational related information.';
  GRANT ALTER ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA" WITH GRANT OPTION;
  GRANT UPDATE ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PBRCSVC";
  GRANT SELECT ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "FCSBILLING";
  GRANT SELECT ON "BILLING_SCHEMA"."OS_ORGANISATION" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index PK_OS_ORGANISATION
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_OS_ORGANISATION" ON "BILLING_SCHEMA"."OS_ORGANISATION" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index UNIQUE_OS_ORG_CODE
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."UNIQUE_OS_ORG_CODE" ON "BILLING_SCHEMA"."OS_ORGANISATION" ("CODE") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_OS_ORGANISATION
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_OS_ORGANISATION" BEFORE INSERT ON BILLING_SCHEMA.OS_ORGANISATION FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_OS_ORGANISATION.NEXTVAL INTO :NEW.ID FROM DUAL; END; 





/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_OS_ORGANISATION" ENABLE;
--------------------------------------------------------
--  Constraints for Table OS_ORGANISATION
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."OS_ORGANISATION" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."OS_ORGANISATION" MODIFY ("CODE" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."OS_ORGANISATION" MODIFY ("ACTIVE_FLAG" NOT NULL ENABLE);
