--------------------------------------------------------
--  DDL for Table PADB_ENC_REGEN_CONTROL
--------------------------------------------------------

  CREATE TABLE "PATIENT_ENQUIRY"."PADB_ENC_REGEN_CONTROL" 
   (	"ENCOUNTER_NUMBER" VARCHAR2(50 CHAR), 
	"START_TIME" TIMESTAMP (6), 
	"COMPLETE_TIME" TIMESTAMP (6), 
	"IS_SKIP" VARCHAR2(1 CHAR)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index IDX_PADB_ENC_REGEN_CONTROL_2
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."IDX_PADB_ENC_REGEN_CONTROL_2" ON "PATIENT_ENQUIRY"."PADB_ENC_REGEN_CONTROL" (TRUNC("START_TIME")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
--------------------------------------------------------
--  DDL for Index IDX_PADB_ENC_REGEN_CONTROL_1
--------------------------------------------------------

  CREATE INDEX "PATIENT_ENQUIRY"."IDX_PADB_ENC_REGEN_CONTROL_1" ON "PATIENT_ENQUIRY"."PADB_ENC_REGEN_CONTROL" ("ENCOUNTER_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;
