--------------------------------------------------------
--  DDL for Table AS_ENCOUNTER_LOCATION
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" 
   (	"ID" NUMBER(15,0), 
	"ENCOUNTER_ID" NUMBER(15,0), 
	"LOCATION_ID" NUMBER(9,0), 
	"LOCATION_BED_TYPE_ID" NUMBER(9,0), 
	"LOCATION_GROUP_ID" NUMBER(9,0), 
	"LOCATION_TYPE_ID" NUMBER(9,0), 
	"START_DATE_TIME" TIMESTAMP (6), 
	"SPECIALTY1_ID" NUMBER(9,0), 
	"SPECIALTY2_ID" NUMBER(9,0), 
	"SUBSPECIALTY1_ID" NUMBER(9,0), 
	"SUBSPECIALTY2_ID" NUMBER(9,0), 
	"SERVICE_TYPE_ID" NUMBER(9,0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION"."ENCOUNTER_ID" IS 'Represents a FK link to a particular encounter.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION"."LOCATION_ID" IS 'The location where a particular encounter occurred (ward1, bed3 etc).';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION"."LOCATION_BED_TYPE_ID" IS 'The bed type allocated for the encounter at this location (clean, befouled, etc).';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION"."LOCATION_GROUP_ID" IS 'Further definition of this location.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION"."LOCATION_TYPE_ID" IS 'The location type of the location at the time in question (ward, bed etc).';
   COMMENT ON COLUMN "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION"."START_DATE_TIME" IS 'The effective start date and time of a particular encounter location.';
   COMMENT ON TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION"  IS 'Stores location details of the encounters';
  GRANT ALTER ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "FCSBILLING";
  GRANT SELECT ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index PK_AS_ENCOUNTER_LOCATION
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_AS_ENCOUNTER_LOCATION" ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" ("ENCOUNTER_ID", "LOCATION_ID", "START_DATE_TIME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENC_LOCATION_LOC_TYPE
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENC_LOCATION_LOC_TYPE" ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" ("LOCATION_TYPE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_ENCOUNTER_LOCATE
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_ENCOUNTER_LOCATE" ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENC_LOCATION_LOC_BED_TYPE
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENC_LOCATION_LOC_BED_TYPE" ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" ("LOCATION_BED_TYPE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENC_LOCATION_LOC_GROUP
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENC_LOCATION_LOC_GROUP" ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" ("LOCATION_GROUP_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ENC_LOCATION_LOCATION
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ENC_LOCATION_LOCATION" ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" ("LOCATION_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index UNIQUE_AS_ENC_LOCATION
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."UNIQUE_AS_ENC_LOCATION" ON "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" ("ENCOUNTER_ID", "START_DATE_TIME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_AS_ENC_LOCATION
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_AS_ENC_LOCATION" BEFORE INSERT ON BILLING_SCHEMA.AS_ENCOUNTER_LOCATION 
FOR EACH ROW 
BEGIN 
SELECT BILLING_SCHEMA.SEQ_AS_ENC_LOCATION.NEXTVAL INTO :NEW.ID FROM DUAL; 
/* --Not required for ATH temp hospital => changed to ICC under NLT
--Simon20201008:start
insert into BILLING_SCHEMA.AS_ENCOUNTER_LOCATION_LOG
select sysdate log_date, 'I' action_type, 'N', :NEW.ID, :NEW.ENCOUNTER_ID, :NEW.LOCATION_ID, :NEW.LOCATION_BED_TYPE_ID, :NEW.LOCATION_GROUP_ID, :NEW.LOCATION_TYPE_ID, :NEW.START_DATE_TIME, :NEW.SPECIALTY1_ID, :NEW.SPECIALTY2_ID, :NEW.SUBSPECIALTY1_ID, :NEW.SUBSPECIALTY2_ID, :NEW.SERVICE_TYPE_ID from dual;
--Simon20201008:end
*/
END;



/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_AS_ENC_LOCATION" ENABLE;
--------------------------------------------------------
--  DDL for Trigger TRIG_ENC_LOCATION_I
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_ENC_LOCATION_I" 
    AFTER DELETE OR INSERT OR UPDATE
    ON BILLING_SCHEMA.AS_ENCOUNTER_LOCATION
    FOR EACH ROW
    DECLARE

    BEGIN
        INSERT INTO BILLING_SCHEMA.PADB_ACTIVITY_LOG
        ( ENCOUNTER_ID, LOG_SOURCE, LOG_DATE_TIME, LOG_PROCESS_STATUS )
        VALUES (
            :NEW.ENCOUNTER_ID, 'AS_ENCOUNTER_LOCATION', SYSTIMESTAMP, 'I'
        );
    END;

/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_ENC_LOCATION_I" ENABLE;
--------------------------------------------------------
--  Constraints for Table AS_ENCOUNTER_LOCATION
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" MODIFY ("ENCOUNTER_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" MODIFY ("LOCATION_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."AS_ENCOUNTER_LOCATION" MODIFY ("LOCATION_TYPE_ID" NOT NULL ENABLE);
