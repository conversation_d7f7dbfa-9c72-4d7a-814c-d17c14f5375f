--------------------------------------------------------
--  DDL for Table BD_TRANS_CHARGE_ALLOC_CODE
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" 
   (	"ID" NUMBER(15,0), 
	"REF_PAYOR_ID" NUMBER(15,0), 
	"CHARGE_ALLOCATION_CODE" VARCHAR2(30 BYTE), 
	"TOTAL_AMT_TO_BE_ALLOCATED" NUMBER(20,2), 
	"TOTAL_AMT_ALLOCATED_TO_DATE" NUMBER(20,2), 
	"TOTAL_QTY_TO_BE_ALLOCATED" NUMBER(15,0), 
	"TOTAL_QTY_ALLOCATED_TO_DATE" NUMBER(15,0), 
	"TRANSACTION_DATE_TIME" TIMESTAMP (6)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
  GRANT ALTER ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" TO "PBRCOPER";
--------------------------------------------------------
--  DDL for Index PK_TRANS_CHG_ALLOC_CODE
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_TRANS_CHG_ALLOC_CODE" ON "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_BD_TRANS_CHARGE_ALLOC
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_BD_TRANS_CHARGE_ALLOC" BEFORE INSERT ON BILLING_SCHEMA.BD_TRANS_CHARGE_ALLOC_CODE FOR EACH ROW BEGIN SELECT BILLING_SCHEMA.SEQ_BD_TRANS_CHARGE_ALLOC_CODE.NEXTVAL INTO :NEW.ID FROM DUAL; END; 





/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_BD_TRANS_CHARGE_ALLOC" ENABLE;
--------------------------------------------------------
--  Constraints for Table BD_TRANS_CHARGE_ALLOC_CODE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" MODIFY ("REF_PAYOR_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_TRANS_CHARGE_ALLOC_CODE" MODIFY ("CHARGE_ALLOCATION_CODE" NOT NULL ENABLE);
