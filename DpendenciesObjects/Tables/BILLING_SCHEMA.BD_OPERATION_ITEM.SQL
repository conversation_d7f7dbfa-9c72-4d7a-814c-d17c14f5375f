--------------------------------------------------------
--  DDL for Table BD_OPERATION_ITEM
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."BD_OPERATION_ITEM" 
   (	"ID" NUMBER(15,0), 
	"OPERATION_ID" NUMBER(15,0), 
	"CHARGE_COMPONENT_ID" NUMBER(15,0), 
	"IS_TAX" CHAR(1 CHAR), 
	"AMOUNT" NUMBER(20,2), 
	"AUTHORISING_USER_ID" NUMBER(9,0), 
	"IS_POSTPONED" VARCHAR2(1 CHAR) DEFAULT 'F', 
	"GL_POSTING_BATCH_NUMBER" VARCHAR2(30 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
  GRANT SELECT ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCSVC";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index FK_BD_OPERATION_CHARGE_COMP
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_BD_OPERATION_CHARGE_COMP" ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" ("CHARGE_COMPONENT_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_BD_OPERATION_ITEM
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_BD_OPERATION_ITEM" ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_BD_OPERATION_ID
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_BD_OPERATION_ID" ON "BILLING_SCHEMA"."BD_OPERATION_ITEM" ("OPERATION_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_OPERATION_ITEM_I
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_OPERATION_ITEM_I" 
    AFTER DELETE OR INSERT OR UPDATE
    ON BILLING_SCHEMA.BD_OPERATION_ITEM
    FOR EACH ROW
    DECLARE
    BEGIN
        IF(:NEW.AMOUNT <> 0) THEN
            INSERT INTO BILLING_SCHEMA.PADB_ACTIVITY_LOG
            ( CHARGE_COMPONENT_ID, LOG_SOURCE, LOG_DATE_TIME, LOG_PROCESS_STATUS )
            VALUES (
                :NEW.CHARGE_COMPONENT_ID, 'BD_OPERATION_ITEM', SYSTIMESTAMP, 'I'
            );
        END IF;
    END;

/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_OPERATION_ITEM_I" ENABLE;
--------------------------------------------------------
--  Constraints for Table BD_OPERATION_ITEM
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION_ITEM" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION_ITEM" MODIFY ("OPERATION_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION_ITEM" MODIFY ("CHARGE_COMPONENT_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION_ITEM" MODIFY ("IS_TAX" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION_ITEM" MODIFY ("AMOUNT" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_OPERATION_ITEM" MODIFY ("IS_POSTPONED" NOT NULL ENABLE);
