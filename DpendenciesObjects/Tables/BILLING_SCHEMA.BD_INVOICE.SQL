--------------------------------------------------------
--  DDL for Table BD_INVOICE
--------------------------------------------------------

  CREATE TABLE "BILLING_SCHEMA"."BD_INVOICE" 
   (	"ID" NUMBER(15,0), 
	"ISSUE_DATE" TIMESTAMP (0), 
	"PAYOR_BILLTO_ID" NUMBER(15,0), 
	"ORGANISATIONAL_ID" NUMBER(9,0), 
	"INVOICE_NUMBER" VARCHAR2(30 CHAR), 
	"DUE_DATE" TIMESTAMP (6), 
	"INVOICE_STATUS" VARCHAR2(1 CHAR), 
	"APPROVAL_STATUS_ID" NUMBER(9,0), 
	"APPROVAL_STATUS_NOTES" VARCHAR2(200 CHAR), 
	"PROFORMA_TEMPLATE_ID" NUMBER(9,0), 
	"FINALISED_TEMPLATE_ID" NUMBER(9,0), 
	"BANNER_MESSAGE" VARCHAR2(300 CHAR), 
	"TYPE_ID" NUMBER(9,0), 
	"IS_CLOSED" VARCHAR2(1 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;

   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_INVOICE"."ISSUE_DATE" IS 'The date a particular invoice was issued.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_INVOICE"."PAYOR_BILLTO_ID" IS 'The entity that will actually be sent the bill to pay.';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_INVOICE"."INVOICE_STATUS" IS 'Indicates if the invoice is Approved(A), UnApproved(P), Cancelled(C).';
   COMMENT ON COLUMN "BILLING_SCHEMA"."BD_INVOICE"."BANNER_MESSAGE" IS 'Stores a general message about an invoice for display to a billing clerk.';
   COMMENT ON TABLE "BILLING_SCHEMA"."BD_INVOICE"  IS 'Stores information on the invoice received by a payor.';
  GRANT SELECT ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCSVC";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT READ ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_INVOICE" TO "HKHA";
  GRANT ALTER ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT DELETE ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT INDEX ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT INSERT ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCNBI";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT UPDATE ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT REFERENCES ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT ON COMMIT REFRESH ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT QUERY REWRITE ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT DEBUG ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT FLASHBACK ON "BILLING_SCHEMA"."BD_INVOICE" TO "PBRCOPER";
  GRANT SELECT ON "BILLING_SCHEMA"."BD_INVOICE" TO "PATIENT_ENQUIRY";
--------------------------------------------------------
--  DDL for Index FK_INVOICE_BD_INVOICE
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_INVOICE_BD_INVOICE" ON "BILLING_SCHEMA"."BD_INVOICE" ("PAYOR_BILLTO_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index FK_ORG_BD_INVOICE
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."FK_ORG_BD_INVOICE" ON "BILLING_SCHEMA"."BD_INVOICE" ("ORGANISATIONAL_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index PK_BD_INVOICE
--------------------------------------------------------

  CREATE UNIQUE INDEX "BILLING_SCHEMA"."PK_BD_INVOICE" ON "BILLING_SCHEMA"."BD_INVOICE" ("ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_INVOICE_LN98
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_INVOICE_LN98" ON "BILLING_SCHEMA"."BD_INVOICE" ("PAYOR_BILLTO_ID", "ORGANISATIONAL_ID", "TYPE_ID", "ID", "ISSUE_DATE", "INVOICE_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index HA_BD_INVOICE_INV_NUM
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."HA_BD_INVOICE_INV_NUM" ON "BILLING_SCHEMA"."BD_INVOICE" ("INVOICE_NUMBER") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_INVOICE_ISSUE_DATE_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_INVOICE_ISSUE_DATE_IDX" ON "BILLING_SCHEMA"."BD_INVOICE" ("ISSUE_DATE") 
  PCTFREE 10 INITRANS 16 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Index BD_INVOICE_INV_NUM_IDX
--------------------------------------------------------

  CREATE INDEX "BILLING_SCHEMA"."BD_INVOICE_INV_NUM_IDX" ON "BILLING_SCHEMA"."BD_INVOICE" (UPPER("INVOICE_NUMBER")) 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS **********
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "BILLING_SCHEMA_INDEX_LARGE" ;
--------------------------------------------------------
--  DDL for Trigger TRIG_BD_INVOICE_I
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "BILLING_SCHEMA"."TRIG_BD_INVOICE_I" 
    AFTER DELETE OR INSERT OR UPDATE
    ON BILLING_SCHEMA.BD_INVOICE
    FOR EACH ROW
    DECLARE

    BEGIN
        INSERT INTO BILLING_SCHEMA.PADB_ACTIVITY_LOG
        ( INVOICE_ID, LOG_SOURCE, LOG_DATE_TIME, LOG_PROCESS_STATUS )
        VALUES (
            :NEW.ID, 'BD_INVOICE', SYSTIMESTAMP, 'I'
        );
    END;

/
ALTER TRIGGER "BILLING_SCHEMA"."TRIG_BD_INVOICE_I" ENABLE;
--------------------------------------------------------
--  Constraints for Table BD_INVOICE
--------------------------------------------------------

  ALTER TABLE "BILLING_SCHEMA"."BD_INVOICE" MODIFY ("ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_INVOICE" MODIFY ("ISSUE_DATE" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_INVOICE" MODIFY ("PAYOR_BILLTO_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_INVOICE" MODIFY ("ORGANISATIONAL_ID" NOT NULL ENABLE);
  ALTER TABLE "BILLING_SCHEMA"."BD_INVOICE" MODIFY ("INVOICE_NUMBER" NOT NULL ENABLE);
