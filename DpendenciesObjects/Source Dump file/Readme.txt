# Create Oracle users
create user PATIENT_01
profile "DEFAULT"
IDENTIFIED by "xxxxxxxx"
DEFAULT TABLESPACE "USERS"
TEMPORARY TABLESPACE "TEMP"
ACCOUNT UNLOCK;

GRANT "CONNECT" TO PATIENT_01;
GRANT "RESOURCE" TO PATIENT_01;
GRANT CREATE SESSION TO PATIENT_01;

ALTER USER "PATIENT_01" QUOTA UNLIMITED ON "USERS";


create user PATIENT_02
profile "DEFAULT"
IDENTIFIED by "xxxxxxxxx"
DEFAULT TABLESPACE "USERS"
TEMPORARY TABLESPACE "TEMP"
ACCOUNT UNLOCK;

GRANT "CONNECT" TO PATIENT_02;
GRANT "RESOURCE" TO PATIENT_02;
GRANT CREATE SESSION TO PATIENT_02;

ALTER USER "PATIENT_02" QUOTA UNLIMITED ON "USERS";


# Import into specific user

impdp  \"/ as sysdba\" TABLE_EXISTS_ACTION=SKIP DUMPFILE=PATIENT_ACCOUNT_DB_REFTABLE_01.dmp DIRECTORY=DATA_PUMP_DIR LOGFILE=PATIENT_ACCOUNT_DB_REFTABLE_imp_01.log, REMAP_SCHEMA=BILLING_SCHEMA:PATIENT_01 REMAP_TABLESPACE=BILLING_SCHEMA_LARGE:USERS,BILLING_SCHEMA_INDEX_LARGE:USERS

impdp  \"/ as sysdba\" TABLE_EXISTS_ACTION=SKIP DUMPFILE=PATIENT_ACCOUNT_DB_01.dmp DIRECTORY=DATA_PUMP_DIR LOGFILE=PATIENT_ACCOUNT_DB_imp_01.log, REMAP_SCHEMA=BILLING_SCHEMA:PATIENT_01 REMAP_TABLESPACE=BILLING_SCHEMA_LARGE:USERS,BILLING_SCHEMA_INDEX_LARGE:USERS

impdp  \"/ as sysdba\" TABLE_EXISTS_ACTION=SKIP DUMPFILE=PATIENT_ACCOUNT_DB_OPERATION_01.dmp DIRECTORY=DATA_PUMP_DIR LOGFILE=PATIENT_ACCOUNT_DB_OPERATION_imp_01.log, REMAP_SCHEMA=BILLING_SCHEMA:PATIENT_01 REMAP_TABLESPACE=BILLING_SCHEMA_LARGE:USERS,BILLING_SCHEMA_INDEX_LARGE:USERS


impdp  \"/ as sysdba\" TABLE_EXISTS_ACTION=SKIP DUMPFILE=PATIENT_ACCOUNT_DB_REFTABLE_01.dmp DIRECTORY=DATA_PUMP_DIR LOGFILE=PATIENT_ACCOUNT_DB_REFTABLE_imp_02.log, REMAP_SCHEMA=BILLING_SCHEMA:PATIENT_02 REMAP_TABLESPACE=BILLING_SCHEMA_LARGE:USERS,BILLING_SCHEMA_INDEX_LARGE:USERS

impdp  \"/ as sysdba\" TABLE_EXISTS_ACTION=SKIP DUMPFILE=PATIENT_ACCOUNT_DB_2_01.dmp DIRECTORY=DATA_PUMP_DIR LOGFILE=PATIENT_ACCOUNT_DB_imp_02.log, REMAP_SCHEMA=BILLING_SCHEMA:PATIENT_02 REMAP_TABLESPACE=BILLING_SCHEMA_LARGE:USERS,BILLING_SCHEMA_INDEX_LARGE:USERS

impdp  \"/ as sysdba\" TABLE_EXISTS_ACTION=SKIP DUMPFILE=PATIENT_ACCOUNT_DB_OPERATION_2_01.dmp DIRECTORY=DATA_PUMP_DIR LOGFILE=PATIENT_ACCOUNT_DB_OPERATION_imp_02.log, REMAP_SCHEMA=BILLING_SCHEMA:PATIENT_02 REMAP_TABLESPACE=BILLING_SCHEMA_LARGE:USERS,BILLING_SCHEMA_INDEX_LARGE:USERS
