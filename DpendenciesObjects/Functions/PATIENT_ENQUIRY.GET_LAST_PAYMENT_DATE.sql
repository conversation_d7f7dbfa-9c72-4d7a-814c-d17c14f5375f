create or replace FUNCTION GET_LAST_PAYMENT_DATE (IN_CHARGE_ID IN NUMBER) RETURN TIMESTAMP IS
    D_RECEIPT_DTM TIMESTAMP(6);
BEGIN
    BEGIN
        SELECT RECEIPT_DTM
        INTO D_RECEIPT_DTM
        FROM (
            SELECT
                TXN.TRANS_TYPE, SUM(TI.AMOUNT) ALLOCATED, MAX(TXN.DATE_TIME_ENTERED) RECEIPT_DTM
            FROM BILLING_SCHEMA.BD_CHARGE_COMPONENT CC
            INNER JOIN BILLING_SCHEMA.BD_TRANSACTION_ITEM TI ON CC.ID = TI.CHARGE_COMPONENT_ID
            INNER JOIN BILLING_SCHEMA.BD_TRANSACTION ALLOC ON TI.TRANSACTION_ID = ALLOC.ID
            INNER JOIN BILLING_SCHEMA.BD_TRANSACTION TXN ON ALLOC.REF_TRANSACTION_ID = TXN.ID
            WHERE CC.CHARGE_ID = IN_CHARGE_ID
            AND   TXN.TRANS_TYPE = 'RC'
            AND   CC.PAYOR_ENTITY_DETAIL_ID = TXN.PAYOR_ENTITY_DETAIL_ID
            GROUP BY TXN.TRANS_TYPE
            HAVING SUM(TI.AMOUNT) <> 0
        );
    EXCEPTION WHEN OTHERS THEN
        D_RECEIPT_DTM := NULL;
    END;

    RETURN D_RECEIPT_DTM;
END GET_LAST_PAYMENT_DATE;